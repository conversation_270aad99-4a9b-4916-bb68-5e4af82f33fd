[{"_id": "672fd9fa89e4140013219d35", "seriesID": "672fd9f889e4140013219d10", "slug": "when-harry-met-sally-1989-1-1", "name": "When <PERSON> Met <PERSON>", "summary": "As <PERSON> and <PERSON> build their lives and careers in Manhattan, their paths continue to cross and their friendship continues to grow … until they confront the decision whether to let their friendship develop into romance.", "description": "As <PERSON> and <PERSON> build their lives and careers in Manhattan, their paths continue to cross and their friendship continues to grow … until they confront the decision whether to let their friendship develop into romance.", "duration": 7200000, "originalContentDuration": 5730000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/672fd9fa89e4140013219d35/screenshot16_9_1751499367878.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/672fd9fa89e4140013219d35/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/672fd9fa89e4140013219d35/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/672fd9fa89e4140013219d35/poster_1751499367188.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/672fd9fa89e4140013219d35/screenshot4_3_1751499365925.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/672fd9fa89e4140013219d35/screenshot16_9_1751499367878.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/672fd9fa89e4140013219d35/poster16_9_1751499365655.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "originalReleaseDate": "1989-07-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "60008ea050e458001a79b39d", "seriesID": "60008e9e50e458001a79b397", "slug": "the-matrix-revolutions-1999-1-1", "name": "The Matrix Revolutions", "summary": "In this final chapter of the Matrix trilogy, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> battle to defend <PERSON> against the onslaught of the machines that have enslaved the human race. And <PERSON> faces the consequences of the choice made in The Matrix Reloaded.", "description": "In this final chapter of the Matrix trilogy, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON> battle to defend <PERSON> against the onslaught of the machines that have enslaved the human race. And <PERSON> faces the consequences of the choice made in The Matrix Reloaded.", "duration": 9900000, "originalContentDuration": 7755000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/60008ea050e458001a79b39d/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/60008ea050e458001a79b39d/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/60008ea050e458001a79b39d/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/60008ea050e458001a79b39d/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/60008ea050e458001a79b39d/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/60008ea050e458001a79b39d/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/60008ea050e458001a79b39d/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Carrie-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2003-11-06T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "68128de9a9356a3062963179", "slug": "dr-phil-primetime", "name": "Dr. <PERSON>", "summary": "The flagship program on <PERSON><PERSON> <PERSON>'s Merit TV is Dr<PERSON> Primetime, a brand-new show from America’s top rated and most successful talk show host.", "description": "The flagship program on <PERSON><PERSON> <PERSON>'s Merit TV is Dr<PERSON> Primetime, a brand-new show from America’s top rated and most successful talk show host.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/68128de9a9356a3062963179/featuredImage_1750967230241.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "News & Information", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/68128de9a9356a3062963179/poster_1750967247424.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/68128de9a9356a3062963179/tile_1750967235973.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/68128de9a9356a3062963179/poster16_9_1750967233720.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "64cd35235f34400013013eeb", "seriesID": "64cd35205f34400013013ec9", "slug": "little-nicky-2000-1-1", "name": "<PERSON>", "summary": "When <PERSON> reneges on his plans to retire, two of his three heirs leave Hell to gather enough souls to vote the <PERSON> of Darkness out of office. But <PERSON> sends his youngest—and nicest—son, <PERSON> <PERSON>, to bring the other two home.", "description": "When <PERSON> reneges on his plans to retire, two of his three heirs leave Hell to gather enough souls to vote the <PERSON> of Darkness out of office. But <PERSON> sends his youngest—and nicest—son, <PERSON> <PERSON>, to bring the other two home.", "duration": 7200000, "originalContentDuration": 5415000, "allotment": 7200, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/64cd35235f34400013013eeb/screenshot16_9_1749682086759.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64cd35235f34400013013eeb/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/64cd35235f34400013013eeb/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64cd35235f34400013013eeb/poster_1749682085916.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64cd35235f34400013013eeb/screenshot4_3_1749682086850.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64cd35235f34400013013eeb/screenshot16_9_1749682086759.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/64cd35235f34400013013eeb/poster16_9_1749682085238.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> \"<PERSON>\" <PERSON>", "Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-11-10T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "683f71cca53b0a8ce0e2c298", "seriesID": "683f71cba53b0a8ce0e2c25c", "slug": "lethal-weapon-3-1992-1-1", "name": "Lethal Weapon 3", "summary": "Police officers <PERSON><PERSON> (<PERSON>) and <PERSON><PERSON><PERSON> (<PERSON>) team for the third time. This time, they must track down a crooked cop who has stolen weapons from the Los Angeles police depot.", "description": "Police officers <PERSON><PERSON> (<PERSON>) and <PERSON><PERSON><PERSON> (<PERSON>) team for the third time. This time, they must track down a crooked cop who has stolen weapons from the Los Angeles police depot.", "duration": 9000000, "originalContentDuration": 7080000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/683f71cca53b0a8ce0e2c298/screenshot16_9_1751349972386.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/683f71cca53b0a8ce0e2c298/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/683f71cca53b0a8ce0e2c298/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/683f71cca53b0a8ce0e2c298/poster_1751349970251.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/683f71cca53b0a8ce0e2c298/screenshot4_3_1751349969084.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/683f71cca53b0a8ce0e2c298/screenshot16_9_1751349972386.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/683f71cca53b0a8ce0e2c298/poster16_9_1751349971327.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1992-05-11T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "629ff609cb032400134d42bc", "seriesID": "629ff605cb032400134d42b5", "slug": "forrest-gump-1994-1-1", "name": "<PERSON>", "summary": "<PERSON> gives an astonishing performance as <PERSON>, an everyman whose simple innocence comes to embody a generation.", "description": "<PERSON> gives an astonishing performance as <PERSON>, an everyman whose simple innocence comes to embody a generation.", "duration": ********, "originalContentDuration": 8535000, "allotment": 10800, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/629ff609cb032400134d42bc/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/629ff609cb032400134d42bc/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/629ff609cb032400134d42bc/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/629ff609cb032400134d42bc/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/629ff609cb032400134d42bc/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/629ff609cb032400134d42bc/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/629ff609cb032400134d42bc/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1994-07-06T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "60008e9d50e458001a79b341", "seriesID": "60008e9b50e458001a79b33b", "slug": "the-matrix-reloaded-1991-1-1", "name": "The Matrix Reloaded", "summary": "In the second chapter of the Matrix trilogy, <PERSON> falls under siege to the Machine Army. <PERSON> and <PERSON> choose to return to the Matrix with Mo<PERSON><PERSON>. Now, <PERSON> must follow the path he has chosen if he is to save humanity.", "description": "In the second chapter of the Matrix trilogy, <PERSON> falls under siege to the Machine Army. <PERSON> and <PERSON> choose to return to the Matrix with Mo<PERSON><PERSON>. Now, <PERSON> must follow the path he has chosen if he is to save humanity.", "duration": ********, "originalContentDuration": 8220000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/60008e9d50e458001a79b341/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/60008e9d50e458001a79b341/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/60008e9d50e458001a79b341/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/60008e9d50e458001a79b341/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/60008e9d50e458001a79b341/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/60008e9d50e458001a79b341/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/60008e9d50e458001a79b341/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Carrie-<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "2003-05-15T23:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "68155df4a9356a306201d60a", "slug": "mad-men-ptv4", "name": "Mad Men", "summary": "Set in 1960s NY, Mad Men pulls the viewer into the high-powered \"Golden Age\" of advertising.", "description": "Set in 1960s NY, Mad Men pulls the viewer into the high-powered \"Golden Age\" of advertising.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/68155df4a9356a306201d60a/featuredImage_1746230772924.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/68155df4a9356a306201d60a/poster_1746692527628.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/68155df4a9356a306201d60a/tile_1746692433033.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/68155df4a9356a306201d60a/poster16_9_1746230773195.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6840e3a14550849057b5b3d6", "seriesID": "6840e3a04550849057b5b3b2", "slug": "youve-got-mail-1998-1-1", "name": "You've Got Mail", "summary": "Multiple Academy Award winner <PERSON> reunites with his <PERSON><PERSON> in Seattle co-star, <PERSON>, and director, <PERSON>, to discover love at first byte in this online romantic comedy.", "description": "Multiple Academy Award winner <PERSON> reunites with his <PERSON><PERSON> in Seattle co-star, <PERSON>, and director, <PERSON>, to discover love at first byte in this online romantic comedy.", "duration": 9000000, "originalContentDuration": 7170000, "allotment": 9000, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/6840e3a14550849057b5b3d6/screenshot16_9_1751499721527.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6840e3a14550849057b5b3d6/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6840e3a14550849057b5b3d6/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6840e3a14550849057b5b3d6/poster_1751499722979.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6840e3a14550849057b5b3d6/screenshot4_3_1751499721782.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6840e3a14550849057b5b3d6/screenshot16_9_1751499721527.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6840e3a14550849057b5b3d6/poster16_9_1751499722057.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1998-12-18T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "68404ff9de5f6440c4f15fd4", "seriesID": "68404ff8de5f6440c4f15faf", "slug": "lethal-weapon-2-1989-1-1", "name": "Lethal Weapon 2", "summary": "<PERSON> and <PERSON> are back as a volatile pair of LAPD detectives. This time, the cops are unhappy with their assignment to baby-sit an accountant. But the mob wants the accountant dead—and is killing everyone who gets in its way.", "description": "<PERSON> and <PERSON> are back as a volatile pair of LAPD detectives. This time, the cops are unhappy with their assignment to baby-sit an accountant. But the mob wants the accountant dead—and is killing everyone who gets in its way.", "duration": 9000000, "originalContentDuration": 6870000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/68404ff9de5f6440c4f15fd4/screenshot16_9_1751350328677.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/68404ff9de5f6440c4f15fd4/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/68404ff9de5f6440c4f15fd4/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/68404ff9de5f6440c4f15fd4/poster_1751350327819.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/68404ff9de5f6440c4f15fd4/screenshot4_3_1751350326885.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/68404ff9de5f6440c4f15fd4/screenshot16_9_1751350328677.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/68404ff9de5f6440c4f15fd4/poster16_9_1751350326876.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1989-07-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6002532ace98800013ebd5fe", "seriesID": "60025328ce98800013ebd5f7", "slug": "the-matrix-1991-1-1", "name": "The Matrix", "summary": "When a beautiful stranger leads computer hacker <PERSON> to a forbidding underworld, he discovers the life he knows is the elaborate deception of an evil cyber-intelligence. Every second becomes a fight to stay alive - to escape The Matrix.", "description": "When a beautiful stranger leads computer hacker <PERSON> to a forbidding underworld, he discovers the life he knows is the elaborate deception of an evil cyber-intelligence. Every second becomes a fight to stay alive - to escape The Matrix.", "duration": ********, "originalContentDuration": 8190000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6002532ace98800013ebd5fe/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6002532ace98800013ebd5fe/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6002532ace98800013ebd5fe/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6002532ace98800013ebd5fe/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6002532ace98800013ebd5fe/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6002532ace98800013ebd5fe/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6002532ace98800013ebd5fe/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Carrie-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1991-03-08T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "61259458256bf20015ae845a", "seriesID": "61259456256bf20015ae8453", "slug": "mean-girls-2004-1-1", "name": "Mean Girls", "summary": "<PERSON><PERSON> thinks she understands \"survival of the fittest.\" However, the law of the wild takes a whole new meaning when the homeschooled 15-year-old enters public high school for the first time.", "description": "<PERSON><PERSON> thinks she understands \"survival of the fittest.\" However, the law of the wild takes a whole new meaning when the homeschooled 15-year-old enters public high school for the first time.", "duration": 7200000, "originalContentDuration": 5820000, "allotment": 7200, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/61259458256bf20015ae845a/screenshot16_9_1749141104032.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/61259458256bf20015ae845a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/61259458256bf20015ae845a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/61259458256bf20015ae845a/poster_1749141125871.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/61259458256bf20015ae845a/screenshot4_3_1749141133855.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/61259458256bf20015ae845a/screenshot16_9_1749141104032.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/61259458256bf20015ae845a/poster16_9_1749141094723.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON><PERSON>"], "originalReleaseDate": "2004-04-30T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "627adefbe26cc40014af410a", "slug": "star-trek-strange-new-worlds", "name": "Star Trek: Strange New Worlds", "summary": "STAR TREK: STRANGE NEW WORLDS is based on the years Captain <PERSON> manned the helm of the U.S.S. Enterprise. The series will follow Captain <PERSON>, Science Officer <PERSON> and <PERSON> <PERSON> as they explore new worlds around the galaxy.", "description": "STAR TREK: STRANGE NEW WORLDS is based on the years Captain <PERSON> manned the helm of the U.S.S. Enterprise. The series will follow Captain <PERSON>, Science Officer <PERSON> and <PERSON> <PERSON> as they explore new worlds around the galaxy.", "originalContentDuration": 0, "rating": "Not Rated", "featuredImage": {"path": "https://images.pluto.tv/series/627adefbe26cc40014af410a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Sci-Fi & Fantasy", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/627adefbe26cc40014af410a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/627adefbe26cc40014af410a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/627adefbe26cc40014af410a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6838ec240f8298da561e112c", "seriesID": "6838ec220f8298da561e1104", "slug": "lethal-weapon-1987-1-1", "name": "Lethal Weapon", "summary": "<PERSON> stars as a Los Angeles policeman who recently lost his wife and has been acting increasingly unstable. <PERSON> plays a homicide detective with an impeccable record and a loving family. Now the two are stuck with each other as partners.", "description": "<PERSON> stars as a Los Angeles policeman who recently lost his wife and has been acting increasingly unstable. <PERSON> plays a homicide detective with an impeccable record and a loving family. Now the two are stuck with each other as partners.", "duration": 9000000, "originalContentDuration": 6585000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6838ec240f8298da561e112c/screenshot16_9_1751349601372.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6838ec240f8298da561e112c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6838ec240f8298da561e112c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6838ec240f8298da561e112c/poster_1751349603327.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6838ec240f8298da561e112c/screenshot4_3_1751349603061.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6838ec240f8298da561e112c/screenshot16_9_1751349601372.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6838ec240f8298da561e112c/poster16_9_1751349602587.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1987-03-06T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "654433cb91ee7c001a83518c", "seriesID": "654433c891ee7c001a835167", "slug": "baywatch-2017-1-1", "name": "Baywatch", "summary": "When a dangerous crime wave hits the beach, Lt. <PERSON> (<PERSON><PERSON>) leads his elite squad of badass lifeguards, including hot-shot recruit <PERSON> (<PERSON><PERSON>), on a mission to prove you don't have to wear a badge to save the bay.", "description": "When a dangerous crime wave hits the beach, Lt. <PERSON> (<PERSON><PERSON>) leads his elite squad of badass lifeguards, including hot-shot recruit <PERSON> (<PERSON><PERSON>), on a mission to prove you don't have to wear a badge to save the bay.", "duration": 9000000, "originalContentDuration": 6990000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/654433cb91ee7c001a83518c/screenshot16_9_1750861864671.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/654433cb91ee7c001a83518c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/654433cb91ee7c001a83518c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/654433cb91ee7c001a83518c/poster_1750861865553.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/654433cb91ee7c001a83518c/screenshot4_3_1750861864871.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/654433cb91ee7c001a83518c/screenshot16_9_1750861864671.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/654433cb91ee7c001a83518c/poster16_9_1751466569549.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2017-05-12T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "ad": true, "cc": true}, {"_id": "6254c85687cec40013564698", "seriesID": "6254c81a87cec40013564690", "slug": "mission-impossible-ghost-protocol-2011-1-1", "name": "Mission: Impossible - Ghost Protocol", "summary": "Agent <PERSON> and his elite team go underground after a bombing implicates them as terrorists. While trying to clear their name, the team uncovers a plot to start a nuclear war.", "description": "Agent <PERSON> and his elite team go underground after a bombing implicates them as terrorists. While trying to clear their name, the team uncovers a plot to start a nuclear war.", "duration": ********, "originalContentDuration": 7980000, "allotment": 10800, "rating": "PG-13", "featuredImage": {"path": "https://mam-assets.clusters.pluto.tv/assets/manual/d4d354b7-0556-441a-993c-3d28d6cbebb4.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6254c85687cec40013564698/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6254c85687cec40013564698/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6254c85687cec40013564698/poster_1751396402783.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6254c85687cec40013564698/screenshot4_3_1751396402319.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://mam-assets.clusters.pluto.tv/assets/manual/d4d354b7-0556-441a-993c-3d28d6cbebb4.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6254c85687cec40013564698/poster16_9_1751396403049.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "2011-12-16T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "61e9e0530a7fe8001a13a7f2", "seriesID": "61e9e0500a7fe8001a13a7eb", "slug": "gladiator-2000-1-1", "name": "Gladiator", "summary": "A man robbed of his name and his dignity strives to win them back, and gain the freedom of his people, in this epic historical drama from director <PERSON>.", "description": "A man robbed of his name and his dignity strives to win them back, and gain the freedom of his people, in this epic historical drama from director <PERSON>.", "duration": 12600000, "originalContentDuration": 9300000, "allotment": 12600, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/61e9e0530a7fe8001a13a7f2/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/61e9e0530a7fe8001a13a7f2/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/61e9e0530a7fe8001a13a7f2/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/61e9e0530a7fe8001a13a7f2/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/61e9e0530a7fe8001a13a7f2/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/61e9e0530a7fe8001a13a7f2/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/61e9e0530a7fe8001a13a7f2/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-05-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5f4eaef761f1b1001aff22b2", "seriesID": "5f4eaef461f1b1001aff22ac", "slug": "shooter-2007-1-1", "name": "Shooter", "summary": "Determined to prove his innocence in a double-cross, <PERSON> is now in a high-tension race from every law enforcement agency in the US and a shadowy organization that wants him dead.", "description": "Determined to prove his innocence in a double-cross, <PERSON> is now in a high-tension race from every law enforcement agency in the US and a shadowy organization that wants him dead.", "duration": 9900000, "originalContentDuration": 7545000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5f4eaef761f1b1001aff22b2/screenshot16_9_1748540163023.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5f4eaef761f1b1001aff22b2/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5f4eaef761f1b1001aff22b2/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5f4eaef761f1b1001aff22b2/poster_1748540164052.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5f4eaef761f1b1001aff22b2/screenshot4_3_1748540162661.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5f4eaef761f1b1001aff22b2/screenshot16_9_1748540163023.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5f4eaef761f1b1001aff22b2/poster16_9_1748540163159.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["[<PERSON>]"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "Lorenzo <PERSON> Bonaventura", "<PERSON><PERSON>"], "originalReleaseDate": "2007-03-23T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6254bcce87cec40013563ff7", "seriesID": "6254bcc887cec40013563fef", "slug": "mission-impossible-ii-2000-1-1", "name": "Mission: Impossible II", "summary": "The world's greatest spy, <PERSON>, returns and partners up with the beautiful Nyah Hall to stop renegade agent <PERSON> from releasing a new kind of terror on an unsuspecting world.", "description": "The world's greatest spy, <PERSON>, returns and partners up with the beautiful Nyah Hall to stop renegade agent <PERSON> from releasing a new kind of terror on an unsuspecting world.", "duration": 9900000, "originalContentDuration": 7425000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/6254bcce87cec40013563ff7/screenshot16_9_1749160457328.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6254bcce87cec40013563ff7/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6254bcce87cec40013563ff7/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6254bcce87cec40013563ff7/poster_1749160457957.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6254bcce87cec40013563ff7/screenshot4_3_1749160457297.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6254bcce87cec40013563ff7/screenshot16_9_1749160457328.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6254bcce87cec40013563ff7/poster16_9_1749160458528.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["Brannon Braga", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-05-24T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "680bbf901426f2773ed8e846", "slug": "extraordinary-world-with-jeff-corwin", "name": "Extraordinary World with <PERSON>", "summary": "Everyday people who are making an extraordinary impact on the world through advocacy, conservation, youth empowerment, and more.", "description": "Everyday people who are making an extraordinary impact on the world through advocacy, conservation, youth empowerment, and more.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/680bbf901426f2773ed8e846/featuredImage_1745600411851.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/680bbf901426f2773ed8e846/poster_1745600421572.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/680bbf901426f2773ed8e846/tile_1745600412147.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/680bbf901426f2773ed8e846/poster16_9_1745600413932.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "60da327bb8187f00136b07f2", "seriesID": "60da2ef91e23180013bf0fbb", "slug": "terminator-2-judgment-day-1991-1-1", "name": "Terminator 2: Judgment Day", "summary": "The non-human machine ruling elite of the nuclear war-torn future has dispatched a cyborg assassin into the past to kill a young boy because he soon will be the resistance leader against the machines.", "description": "The non-human machine ruling elite of the nuclear war-torn future has dispatched a cyborg assassin into the past to kill a young boy because he soon will be the resistance leader against the machines.", "duration": ********, "originalContentDuration": 8235000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/60da327bb8187f00136b07f2/screenshot16_9_1748355841903.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/60da327bb8187f00136b07f2/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/60da327bb8187f00136b07f2/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/60da327bb8187f00136b07f2/poster_1748355841786.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/60da327bb8187f00136b07f2/screenshot4_3_1748355842760.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/60da327bb8187f00136b07f2/screenshot16_9_1748355841903.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/60da327bb8187f00136b07f2/poster16_9_1748355843130.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1991-07-03T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6074d28a918e16001a511eed", "seriesID": "6074d288918e16001a511ee6", "slug": "jack-ryan-shadow-recruit-2014-1-1", "name": "<PERSON>: Shadow Recruit", "summary": "<PERSON> must quickly evolve from soldier to analyst to full-fledged operative to stop a devastating terrorist plot against the United States.", "description": "<PERSON> must quickly evolve from soldier to analyst to full-fledged operative to stop a devastating terrorist plot against the United States.", "duration": 8100000, "originalContentDuration": 6345000, "allotment": 8100, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/6074d28a918e16001a511eed/screenshot16_9_1747865161220.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6074d28a918e16001a511eed/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6074d28a918e16001a511eed/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6074d28a918e16001a511eed/poster_1747865169143.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6074d28a918e16001a511eed/screenshot4_3_1747865165724.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6074d28a918e16001a511eed/screenshot16_9_1747865161220.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6074d28a918e16001a511eed/poster16_9_1747865163761.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2014-01-17T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6255bf9378f6d300136e5245", "seriesID": "6255bf8878f6d300136e523d", "slug": "mission-impossible-1996-1996-1-1", "name": "Mission: Impossible", "summary": "<PERSON>, a secret agent, is framed for the deaths of his espionage team and attempts to flee government assassins after him. He races against the clock in order to discover the truth.", "description": "<PERSON>, a secret agent, is framed for the deaths of his espionage team and attempts to flee government assassins after him. He races against the clock in order to discover the truth.", "duration": 9000000, "originalContentDuration": 6615000, "allotment": 9000, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/6255bf9378f6d300136e5245/screenshot16_9_1748392922996.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6255bf9378f6d300136e5245/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6255bf9378f6d300136e5245/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6255bf9378f6d300136e5245/poster_1748392923007.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6255bf9378f6d300136e5245/screenshot4_3_1748392923015.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6255bf9378f6d300136e5245/screenshot16_9_1748392922996.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6255bf9378f6d300136e5245/poster16_9_1748392921996.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1996-05-22T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "67858a19ce63b501e7d469a1", "slug": "hangin-with-mr-cooper", "name": "Hangin' With Mr. <PERSON>", "summary": "Just out of school and on his own for the first time, former college basketball superstar <PERSON> shares a house with two beautiful women—his best friend and her roommate. <PERSON> also happens to be the coolest substitute teacher around.", "description": "Just out of school and on his own for the first time, former college basketball superstar <PERSON> shares a house with two beautiful women—his best friend and her roommate. <PERSON> also happens to be the coolest substitute teacher around.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/67858a19ce63b501e7d469a1/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/67858a19ce63b501e7d469a1/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/67858a19ce63b501e7d469a1/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/67858a19ce63b501e7d469a1/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "64558202eead4c001bdc02a4", "seriesID": "64558200eead4c001bdc027d", "slug": "tropic-thunder-2008-1-1", "name": "Tropic Thunder", "summary": "<PERSON>, <PERSON> and <PERSON> lead an ensemble cast in 'Tropic Thunder,' an action comedy about a group of self-absorbed actors who set out to make the most expensive war film.", "description": "<PERSON>, <PERSON> and <PERSON> lead an ensemble cast in 'Tropic Thunder,' an action comedy about a group of self-absorbed actors who set out to make the most expensive war film.", "duration": 8100000, "originalContentDuration": 6405000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/64558202eead4c001bdc02a4/screenshot16_9_1744930446502.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64558202eead4c001bdc02a4/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/64558202eead4c001bdc02a4/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64558202eead4c001bdc02a4/poster_1744930450006.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64558202eead4c001bdc02a4/screenshot4_3_1744930443923.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64558202eead4c001bdc02a4/screenshot16_9_1744930446502.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "ratingDescriptors": ["us-d", "us-l", "us-v", "us-smoking"], "poster16_9": {"path": "https://images.pluto.tv/episodes/64558202eead4c001bdc02a4/poster16_9_1744930448477.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2008-08-13T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "682ca9d27de8748e63f096a4", "seriesID": "682ca9d07de8748e63f0967d", "slug": "28-days-later-2002-1-1", "name": "28 Days Later", "summary": "Animal rights activists free a group of infected chimps to horrifying results. 28 days later, <PERSON> wakes from a coma and explores a deserted London. He soon learns the truth behind the deserted streets and the menacing creatures that lurk in the shadows.", "description": "Animal rights activists free a group of infected chimps to horrifying results. 28 days later, <PERSON> wakes from a coma and explores a deserted London. He soon learns the truth behind the deserted streets and the menacing creatures that lurk in the shadows.", "duration": 9000000, "originalContentDuration": 6840000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/682ca9d27de8748e63f096a4/screenshot16_9_1748374202921.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/682ca9d27de8748e63f096a4/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/682ca9d27de8748e63f096a4/poster_1748374203695.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/682ca9d27de8748e63f096a4/screenshot4_3_1748374202619.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/682ca9d27de8748e63f096a4/screenshot16_9_1748374202921.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/682ca9d27de8748e63f096a4/poster16_9_1748374202529.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2002-10-16T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "670452db1a51490013b21374", "seriesID": "670452d81a51490013b2134d", "slug": "transformers-age-of-extinction-2014-1-1", "name": "Transformers: Age of Extinction", "summary": "From director <PERSON> comes the best 'Transformers' ever! With humanity facing extinction from a terrifying new threat, it's up to the Autobots to save Earth. They'll need new allies, including inventor <PERSON> (<PERSON>) and the Dinobots!", "description": "From director <PERSON> comes the best 'Transformers' ever! With humanity facing extinction from a terrifying new threat, it's up to the Autobots to save Earth. They'll need new allies, including inventor <PERSON> (<PERSON>) and the Dinobots!", "duration": 12600000, "originalContentDuration": 9915000, "allotment": 12600, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/670452db1a51490013b21374/screenshot16_9_1749488404025.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/670452db1a51490013b21374/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/670452db1a51490013b21374/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/670452db1a51490013b21374/poster_1749488403534.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/670452db1a51490013b21374/screenshot4_3_1749488402022.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/670452db1a51490013b21374/screenshot16_9_1749488404025.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/670452db1a51490013b21374/poster16_9_1749488402708.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Bingbing Li", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>", "Lorenzo <PERSON> Bonaventura"], "originalReleaseDate": "2014-06-27T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "629ff600cb032400134d419d", "seriesID": "629ff5fdcb032400134d4195", "slug": "beverly-hills-cop-1984-1-1", "name": "Beverly Hills Cop", "summary": "The heat is on in this fast-paced action-comedy starring <PERSON> as <PERSON>, a street smart Detroit cop tracking down his best friend's killer in Beverly Hills.", "description": "The heat is on in this fast-paced action-comedy starring <PERSON> as <PERSON>, a street smart Detroit cop tracking down his best friend's killer in Beverly Hills.", "duration": 8100000, "originalContentDuration": 6315000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/629ff600cb032400134d419d/screenshot16_9_1747413362739.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/629ff600cb032400134d419d/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/629ff600cb032400134d419d/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/629ff600cb032400134d419d/poster_1747413360690.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/629ff600cb032400134d419d/screenshot4_3_1747413365350.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/629ff600cb032400134d419d/screenshot16_9_1747413362739.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/629ff600cb032400134d419d/poster16_9_1747413364237.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Judge <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1984-12-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "653b35c0619680001a413d3e", "slug": "gomer-pyle-usmc", "name": "<PERSON><PERSON>, U.S.M.C.", "summary": "<PERSON><PERSON>, a naive country boy, leaves his home in Mayberry, NC to join the U.S. Marine corps. His perpetual wide-eyed innocence frequently gets on the nerves of his tough, loudmouthed sergeant.", "description": "<PERSON><PERSON>, a naive country boy, leaves his home in Mayberry, NC to join the U.S. Marine corps. His perpetual wide-eyed innocence frequently gets on the nerves of his tough, loudmouthed sergeant.", "originalContentDuration": 0, "rating": "TV-G", "featuredImage": {"path": "https://images.pluto.tv/series/653b35c0619680001a413d3e/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/653b35c0619680001a413d3e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/653b35c0619680001a413d3e/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/653b35c0619680001a413d3e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "607dc602f96a44001a1df8b8", "seriesID": "607dc5fff96a44001a1df8b1", "slug": "four-brothers-2005-1-1", "name": "Four Brothers", "summary": "After their adoptive mother is murdered during a grocery store hold-up, the Mercer bros reunite to take the matter into their own hands. As they track down the killer, they quickly realize that their old ways of doing business have new consequences.", "description": "After their adoptive mother is murdered during a grocery store hold-up, the Mercer bros reunite to take the matter into their own hands. As they track down the killer, they quickly realize that their old ways of doing business have new consequences.", "duration": 9000000, "originalContentDuration": 6525000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/607dc602f96a44001a1df8b8/screenshot16_9_1747414801188.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/607dc602f96a44001a1df8b8/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/607dc602f96a44001a1df8b8/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/607dc602f96a44001a1df8b8/poster_1747414801804.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/607dc602f96a44001a1df8b8/screenshot4_3_1747414802693.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/607dc602f96a44001a1df8b8/screenshot16_9_1747414801188.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/607dc602f96a44001a1df8b8/poster16_9_1747414806030.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"originalReleaseDate": "2005-08-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "644885a2dd38b4001a8c8e2f", "seriesID": "644885a0dd38b4001a8c8e0c", "slug": "the-fifth-element-1997-1-1", "name": "The Fifth Element", "summary": "<PERSON>, <PERSON><PERSON>, and <PERSON> star in acclaimed director <PERSON>'s outrageous sci-fi adventure, an extravagantly styled tale of good against evil set in an unbelievable twenty-third century world.", "description": "<PERSON>, <PERSON><PERSON>, and <PERSON> star in acclaimed director <PERSON>'s outrageous sci-fi adventure, an extravagantly styled tale of good against evil set in an unbelievable twenty-third century world.", "duration": 9900000, "originalContentDuration": 7560000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/644885a2dd38b4001a8c8e2f/screenshot16_9_1751486801469.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/644885a2dd38b4001a8c8e2f/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/644885a2dd38b4001a8c8e2f/poster_1751486801192.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/644885a2dd38b4001a8c8e2f/screenshot4_3_1751486801157.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/644885a2dd38b4001a8c8e2f/screenshot16_9_1751486801469.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/644885a2dd38b4001a8c8e2f/poster16_9_1751486801381.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON><PERSON>"], "originalReleaseDate": "1997-05-07T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "67e4669a516f910739e7e35e", "seriesID": "67e46698516f910739e7e32c", "slug": "jerry-maguire-1996-1-1", "name": "<PERSON>", "summary": "<PERSON> is a sports agent who is brought to a point of crisis in his life when he decides to become an honorable man. Follow his journey to redemption through an unlikely alliance with a female accountant, and his least important client.", "description": "<PERSON> is a sports agent who is brought to a point of crisis in his life when he decides to become an honorable man. Follow his journey to redemption through an unlikely alliance with a female accountant, and his least important client.", "duration": ********, "originalContentDuration": 8325000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/67e4669a516f910739e7e35e/screenshot16_9_1746222482999.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/67e4669a516f910739e7e35e/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/67e4669a516f910739e7e35e/poster_1746222484099.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/67e4669a516f910739e7e35e/screenshot4_3_1746691442749.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/67e4669a516f910739e7e35e/screenshot16_9_1746222482999.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/67e4669a516f910739e7e35e/poster16_9_1746222483715.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "Cuba Gooding Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Regina King", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1996-12-06T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5e741ea47255220014470334", "seriesID": "5e741ea2725522001447032c", "slug": "kiss-the-girls-1996-1-1", "name": "Kiss the Girls", "summary": "Eight kidnapped women. All beautiful. All talented. All in danger of having their lives cut cruelly short if police detective <PERSON> and key witness <PERSON> can’t locate the elusive “collector” who calls himself <PERSON><PERSON>.", "description": "Eight kidnapped women. All beautiful. All talented. All in danger of having their lives cut cruelly short if police detective <PERSON> and key witness <PERSON> can’t locate the elusive “collector” who calls himself <PERSON><PERSON>.", "duration": 9000000, "originalContentDuration": 6945000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5e741ea47255220014470334/screenshot16_9_1748392203125.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5e741ea47255220014470334/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5e741ea47255220014470334/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5e741ea47255220014470334/poster_1748392204503.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5e741ea47255220014470334/screenshot4_3_1748392204496.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5e741ea47255220014470334/screenshot16_9_1748392203125.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5e741ea47255220014470334/poster16_9_1748392204470.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1997-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5efd15b89a61fa001acdd1dd", "seriesID": "5efd15b19a61fa001acdd1d4", "slug": "days-of-thunder-1989-1-1", "name": "Days of Thunder", "summary": "Discovered by businessman <PERSON>, <PERSON> is teamed with legendary crew chief and car-builder <PERSON> to race for the Winston Cup at the Daytona 500. A crash nearly ends <PERSON>’s career and he must turn to a beautiful doctor to regain his nerve.", "description": "Discovered by businessman <PERSON>, <PERSON> is teamed with legendary crew chief and car-builder <PERSON> to race for the Winston Cup at the Daytona 500. A crash nearly ends <PERSON>’s career and he must turn to a beautiful doctor to regain his nerve.", "duration": 8100000, "originalContentDuration": 6450000, "allotment": 8100, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5efd15b89a61fa001acdd1dd/screenshot16_9_1748023203483.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5efd15b89a61fa001acdd1dd/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5efd15b89a61fa001acdd1dd/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5efd15b89a61fa001acdd1dd/poster_1748023201860.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5efd15b89a61fa001acdd1dd/screenshot4_3_1748023201848.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5efd15b89a61fa001acdd1dd/screenshot16_9_1748023203483.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5efd15b89a61fa001acdd1dd/poster16_9_1748023202478.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1990-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "63dadb40200a67001387a965", "slug": "i-love-lucy-ptv5", "name": "I Love Lucy", "summary": "This classic sitcom centers around the hilarious adventures, and misadventures, of <PERSON> and her bandleader husband <PERSON>.", "description": "This classic sitcom centers around the hilarious adventures, and misadventures, of <PERSON> and her bandleader husband <PERSON>.", "originalContentDuration": 0, "rating": "TV-G", "featuredImage": {"path": "https://images.pluto.tv/series/63dadb40200a67001387a965/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/63dadb40200a67001387a965/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/63dadb40200a67001387a965/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/63dadb40200a67001387a965/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5eed2e5cf44321001a7b6d58", "seriesID": "5eed2e59f44321001a7b6d4f", "slug": "the-firm-1993-1-1", "name": "The Firm", "summary": "<PERSON> joins a prosperous Memphis firm that affords <PERSON> and his wife an affluent lifestyle beyond their wildest dreams. But when FBI agents confront him with evidence of corruption and murder within the firm, <PERSON> sets out to find the truth.", "description": "<PERSON> joins a prosperous Memphis firm that affords <PERSON> and his wife an affluent lifestyle beyond their wildest dreams. But when FBI agents confront him with evidence of corruption and murder within the firm, <PERSON> sets out to find the truth.", "duration": 12600000, "originalContentDuration": 9285000, "allotment": 12600, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5eed2e5cf44321001a7b6d58/screenshot16_9_1748538003114.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5eed2e5cf44321001a7b6d58/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5eed2e5cf44321001a7b6d58/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5eed2e5cf44321001a7b6d58/poster_1748538004128.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5eed2e5cf44321001a7b6d58/screenshot4_3_1748538004071.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5eed2e5cf44321001a7b6d58/screenshot16_9_1748538003114.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5eed2e5cf44321001a7b6d58/poster16_9_1748538004146.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>ack"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>ack"], "originalReleaseDate": "1993-06-30T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "629ff604cb032400134d4222", "seriesID": "629ff601cb032400134d421a", "slug": "beverly-hills-cop-ii-1987-1-1-ptv1", "name": "Beverly Hills Cop II", "summary": "The heat's back on! And <PERSON> is cool as ever in this sizzling, smash-hit sequel to Beverly Hills Cop.", "description": "The heat's back on! And <PERSON> is cool as ever in this sizzling, smash-hit sequel to Beverly Hills Cop.", "duration": 8100000, "originalContentDuration": 6180000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/629ff604cb032400134d4222/screenshot16_9_1751385241807.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/629ff604cb032400134d4222/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/629ff604cb032400134d4222/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/629ff604cb032400134d4222/poster_1751385242033.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/629ff604cb032400134d4222/screenshot4_3_1751385242283.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/629ff604cb032400134d4222/screenshot16_9_1751385241807.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/629ff604cb032400134d4222/poster16_9_1751385241581.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Judge <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1987-05-20T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5c48dc671390b6759598cc9c", "seriesID": "5c48dc671390b6759598cc90", "slug": "beverly-hills-cop-iii-1-1", "name": "Beverly Hills Cop III", "summary": "<PERSON>'s back! So are the laughs. <PERSON> returns as Detroit cop <PERSON>, getting down with the fun-and-sun set as never before in this revved up thrill-ride of a movie.", "description": "<PERSON>'s back! So are the laughs. <PERSON> returns as Detroit cop <PERSON>, getting down with the fun-and-sun set as never before in this revved-up thrill-ride of a movie.", "duration": 8100000, "originalContentDuration": 6255000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5c48dc671390b6759598cc9c/screenshot16_9_1748014923191.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5c48dc671390b6759598cc9c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5c48dc671390b6759598cc9c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5c48dc671390b6759598cc9c/poster_1748014923276.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5c48dc671390b6759598cc9c/screenshot4_3_1748014923389.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5c48dc671390b6759598cc9c/screenshot16_9_1748014923191.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5c48dc671390b6759598cc9c/poster16_9_1748014923102.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1993-12-31T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "67a5fcc809607d687e467d3a", "slug": "eve", "name": "Eve", "summary": "Following <PERSON> and her group of friends as they experience the highs and lows of life.", "description": "Following <PERSON> and her group of friends as they experience the highs and lows of life.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/67a5fcc809607d687e467d3a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/67a5fcc809607d687e467d3a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/67a5fcc809607d687e467d3a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/67a5fcc809607d687e467d3a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "647f5539a7ef77001ae843ab", "seriesID": "647f5536a7ef77001ae84388", "slug": "xxx-2002-1-1", "name": "XXX", "summary": "<PERSON><PERSON> is a notorious underground thrill-seeker deemed untouchable by the law. But when NSA Agent <PERSON><PERSON> convinces <PERSON> to infiltrate a ruthless Russian crime ring, this new breed of secret agent takes down the enemies of justice with a vengeance.", "description": "<PERSON><PERSON> is a notorious underground thrill-seeker deemed untouchable by the law. But when NSA Agent <PERSON><PERSON> convinces <PERSON> to infiltrate a ruthless Russian crime ring, this new breed of secret agent takes down the enemies of justice with a vengeance.", "duration": ********, "originalContentDuration": 7965000, "allotment": 10800, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/647f5539a7ef77001ae843ab/screenshot16_9_1747865539449.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/647f5539a7ef77001ae843ab/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/647f5539a7ef77001ae843ab/poster_1747865538855.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/647f5539a7ef77001ae843ab/screenshot4_3_1747865532713.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/647f5539a7ef77001ae843ab/screenshot16_9_1747865539449.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/647f5539a7ef77001ae843ab/poster16_9_1747865540216.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "Vin Diesel", "Asia Argento", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2002-08-08T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "616872fc0b4e8f001a960443", "seriesID": "616872f90b4e8f001a96043c", "slug": "titanic-1997-1-1", "name": "Titanic", "summary": "<PERSON> and Oscar-nominee <PERSON> light up the screen as <PERSON> and <PERSON>, the young lovers who find one another on the maiden voyage of the \"unsinkable\" R.M.S. Titanic.", "description": "<PERSON> and Oscar-nominee <PERSON> light up the screen as <PERSON> and <PERSON>, the young lovers who find one another on the maiden voyage of the \"unsinkable\" R.M.S. Titanic.", "duration": 15300000, "originalContentDuration": 11700000, "allotment": 15300, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/616872fc0b4e8f001a960443/screenshot16_9_1748888030891.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/616872fc0b4e8f001a960443/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/616872fc0b4e8f001a960443/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/616872fc0b4e8f001a960443/poster_1748888001349.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/616872fc0b4e8f001a960443/screenshot4_3_1748888030847.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/616872fc0b4e8f001a960443/screenshot16_9_1748888030891.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/616872fc0b4e8f001a960443/poster16_9_1748888002260.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1997-12-19T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "602abaabeac9500013145983", "seriesID": "602abaa9eac950001314597c", "slug": "the-wolf-of-wall-street-2013-1-1", "name": "The Wolf of Wall Street", "summary": "Sex. Money. Power. Drugs.  Brace yourself for a true story from legendary filmmaker <PERSON>. <PERSON> stars as a young stockbroker hungry for a life of non-stop thrills, where corruption was king and more was never enough.", "description": "Sex. Money. Power. Drugs.  Brace yourself for a true story from legendary filmmaker <PERSON>. <PERSON> stars as a young stockbroker hungry for a life of non-stop thrills, where corruption was king and more was never enough.", "duration": 13500000, "originalContentDuration": ********, "allotment": 13500, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/602abaabeac9500013145983/screenshot16_9_1748544121667.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/602abaabeac9500013145983/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/602abaabeac9500013145983/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/602abaabeac9500013145983/poster_1748544122405.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/602abaabeac9500013145983/screenshot4_3_1748544123145.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/602abaabeac9500013145983/screenshot16_9_1748544121667.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/602abaabeac9500013145983/poster16_9_1748544123017.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2013-12-25T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "680a1f52a21058b98f863512", "seriesID": "680a1f50a21058b98f8634ec", "slug": "mission-impossible-rogue-nation-2018-1-1", "name": "Mission: Impossible - Rogue Nation", "summary": "<PERSON> and team take on their most impossible mission yet, eradicating the Syndicate - an International rogue organization as highly skilled as they are, committed to destroying the IMF.", "description": "<PERSON> and team take on their most impossible mission yet, eradicating the Syndicate - an International rogue organization as highly skilled as they are, committed to destroying the IMF.", "duration": 9900000, "originalContentDuration": 7890000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/680a1f52a21058b98f863512/screenshot16_9_1748906397495.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/680a1f52a21058b98f863512/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/680a1f52a21058b98f863512/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/680a1f52a21058b98f863512/poster_1749504691751.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/680a1f52a21058b98f863512/screenshot4_3_1748906396354.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/680a1f52a21058b98f863512/screenshot16_9_1748906397495.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/680a1f52a21058b98f863512/poster16_9_1748906396284.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "2018-02-24T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5ee7b76bb7841f001a87398f", "slug": "threes-company-series", "name": "Three's Company", "summary": "The misadventures of two women and one man living in one apartment and their neighbors.", "description": "The misadventures of two women and one man living in one apartment and their neighbors.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5ee7b76bb7841f001a87398f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5ee7b76bb7841f001a87398f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5ee7b76bb7841f001a87398f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5ee7b76bb7841f001a87398f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "680aa650afe56ba72ed0a6e3", "slug": "mission-unstoppable-with-miranda-cosgrove", "name": "Mission Unstoppable with <PERSON>", "summary": "Host <PERSON> spotlights female STEM superstars who dominate their fields and shows young viewers how these inspiring women are unstoppable.", "description": "Host <PERSON> spotlights female STEM superstars who dominate their fields and shows young viewers how these inspiring women are unstoppable.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/680aa650afe56ba72ed0a6e3/featuredImage_1745528406832.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/680aa650afe56ba72ed0a6e3/poster_1745528406648.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/680aa650afe56ba72ed0a6e3/tile_1745528406590.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/680aa650afe56ba72ed0a6e3/poster16_9_1745528408720.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6362d618bc198f0013349589", "slug": "cheers-ptv2", "name": "Cheers", "summary": "Cheers premiered in 1982 and the story about a blue-collar Boston bar run by former sports star <PERSON> and the quirky people who worked and drank there quickly captured America's heart.", "description": "Cheers premiered in 1982 and the story about a blue-collar Boston bar run by former sports star <PERSON> and the quirky people who worked and drank there quickly captured America's heart.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/6362d618bc198f0013349589/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 9, 10, 11], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6362d618bc198f0013349589/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6362d618bc198f0013349589/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6362d618bc198f0013349589/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "679ab2fc7641390014c38e14", "seriesID": "679ab2fa7641390014c38ded", "slug": "monty-pythons-life-of-brian-1979-1-1", "name": "<PERSON>'s Life of Brian", "summary": "A notorious religious satire, the film tells the story of <PERSON> (<PERSON>), a reluctant would-be messiah who rises to prominence as a result of a series of absurd and truly hilarious circumstances.", "description": "A notorious religious satire, the film tells the story of <PERSON> (<PERSON>), a reluctant would-be messiah who rises to prominence as a result of a series of absurd and truly hilarious circumstances.", "duration": 7200000, "originalContentDuration": 5640000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/679ab2fc7641390014c38e14/screenshot16_9_1749512522179.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/679ab2fc7641390014c38e14/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/679ab2fc7641390014c38e14/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/679ab2fc7641390014c38e14/poster_1749516869835.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/679ab2fc7641390014c38e14/screenshot4_3_1749516871631.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/679ab2fc7641390014c38e14/screenshot16_9_1749512522179.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/679ab2fc7641390014c38e14/poster16_9_1749512521801.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1979-08-17T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66fdb024b99f8500131020f4", "slug": "matlock-ptv9", "name": "<PERSON><PERSON> (Latest Episodes)", "summary": "Emmy and Academy Award winner <PERSON> stars as <PERSON>, a brilliant lawyer who uses her unassuming demeanor to win cases.", "description": "Emmy and Academy Award winner <PERSON> stars as <PERSON>, a brilliant lawyer who uses her unassuming demeanor to win cases.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66fdb024b99f8500131020f4/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66fdb024b99f8500131020f4/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66fdb024b99f8500131020f4/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66fdb024b99f8500131020f4/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "681941b430b839eca53aa391", "slug": "the-visioneers-with-zay-harding", "name": "The Visioneers With <PERSON><PERSON>", "summary": "Weekly eco-adventure series, hosted by international explorer <PERSON><PERSON>, showcases the remarkable scientists, engineers, and people with innovative ideas who are creating environmental solutions.", "description": "Weekly eco-adventure series, hosted by international explorer <PERSON><PERSON>, showcases the remarkable scientists, engineers, and people with innovative ideas who are creating environmental solutions.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/681941b430b839eca53aa391/featuredImage_1746485684697.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/681941b430b839eca53aa391/poster_1746704094942.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/681941b430b839eca53aa391/tile_1746485685284.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/681941b430b839eca53aa391/poster16_9_1746485685342.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "67edb79d89ba442cd9a895bc", "seriesID": "67edb79b89ba442cd9a89595", "slug": "dont-tell-mom-the-babysitters-dead-2024-2024-1-1", "name": "Don't Tell Mom the Babysitter's Dead (2024)", "summary": "<PERSON> finds her summer plans canceled when her mom jets off for a last-minute retreat and the elderly babysitter who arrives at her door unexpectedly passes away.", "description": "<PERSON> finds her summer plans canceled when her mom jets off for a last-minute retreat and the elderly babysitter who arrives at her door unexpectedly passes away.", "duration": 8100000, "originalContentDuration": 5940000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/67edb79d89ba442cd9a895bc/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/67edb79d89ba442cd9a895bc/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/67edb79d89ba442cd9a895bc/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/67edb79d89ba442cd9a895bc/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/67edb79d89ba442cd9a895bc/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/67edb79d89ba442cd9a895bc/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/67edb79d89ba442cd9a895bc/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "June <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>-<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2024-04-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66d5f1e322dfe900130ad5c1", "slug": "the-closer", "name": "The Closer", "summary": "<PERSON><PERSON> plays a CIA-trained detective brought from Atlanta to Los Angeles to head up the Priority Murder Squad, a special unit of the LAPD that handles sensitive, high-profile murder cases.", "description": "<PERSON><PERSON> plays a CIA-trained detective brought from Atlanta to Los Angeles to head up the Priority Murder Squad, a special unit of the LAPD that handles sensitive, high-profile murder cases.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66d5f1e322dfe900130ad5c1/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66d5f1e322dfe900130ad5c1/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66d5f1e322dfe900130ad5c1/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66d5f1e322dfe900130ad5c1/poster16_9_1731026647125.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "61e9ea7bf30167001a6d270d", "seriesID": "61e9ea78f30167001a6d2706", "slug": "mission-impossible-iii-2006-1-1", "name": "Mission: Impossible III", "summary": "<PERSON> hurdles into spectacular new adventures from Rome to Shanghai as he races to rescue a captured agent and stop <PERSON><PERSON> from eliminating his next target, <PERSON>’s wife.", "description": "<PERSON> hurdles into spectacular new adventures from Rome to Shanghai as he races to rescue a captured agent and stop <PERSON><PERSON> from eliminating his next target, <PERSON>’s wife.", "duration": 9900000, "originalContentDuration": 7530000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/61e9ea7bf30167001a6d270d/screenshot16_9_1748454126307.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/61e9ea7bf30167001a6d270d/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/61e9ea7bf30167001a6d270d/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/61e9ea7bf30167001a6d270d/poster_1748454127125.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/61e9ea7bf30167001a6d270d/screenshot4_3_1748454127988.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/61e9ea7bf30167001a6d270d/screenshot16_9_1748454126307.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/61e9ea7bf30167001a6d270d/poster16_9_1748454127477.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON><PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2006-05-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "680aa663afe56ba72ed352b1", "slug": "lucky-dog-reunions", "name": "Lucky Dog Reunions", "summary": "Animal trainer <PERSON> revisits his most heartwarming dog rescue stories, training and pairing them with a forever family.", "description": "Animal trainer <PERSON> revisits his most heartwarming dog rescue stories, training and pairing them with a forever family.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/680aa663afe56ba72ed352b1/featuredImage_1745528420296.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [12], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/680aa663afe56ba72ed352b1/poster_1745528420452.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/680aa663afe56ba72ed352b1/tile_1745528420489.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/680aa663afe56ba72ed352b1/poster16_9_1745528420556.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "67fe936603a2ab14dacf457e", "seriesID": "67fe936503a2ab14dacf4556", "slug": "57-seconds-2024-1-1", "name": "57 Seconds", "summary": "<PERSON> and <PERSON> star in this heart-racing action thriller. Every second counts for a tech blogger who wields a time-altering device to seek revenge on a ruthless corporate empire and ignites a pulse-pounding battle for survival.", "description": "<PERSON> (The Hunger Games) and Academy Award® winner <PERSON> (Million Dollar Baby) star in this heart-racing action thriller. When a tech blogger discovers a time-altering device, he unleashes its power to rewrite the past and seek revenge against the ruthless corporate empire that destroyed his family. But his actions soon trigger a terrifying chain of events, propelling him into a pulse-pounding battle for survival where every second counts.", "duration": 8100000, "originalContentDuration": 5970000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/67fe936603a2ab14dacf457e/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/67fe936603a2ab14dacf457e/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/67fe936603a2ab14dacf457e/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/67fe936603a2ab14dacf457e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/67fe936603a2ab14dacf457e/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/67fe936603a2ab14dacf457e/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/67fe936603a2ab14dacf457e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "originalReleaseDate": "2024-09-29T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6789b3d0daa34700140819b7", "slug": "twin-peaks-ptv5", "name": "Twin Peaks", "summary": "F.B.I. Agent <PERSON> comes to the logging town of Twin Peaks to investigate the murder of <PERSON>, a beautiful and popular high school student. While investigating, Agent <PERSON> slowly learns that strange things are happening.", "description": "F.B.I. Agent <PERSON> comes to the logging town of Twin Peaks to investigate the murder of <PERSON>, a beautiful and popular high school student. While investigating, Agent <PERSON> slowly learns that strange things are happening.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/6789b3d0daa34700140819b7/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6789b3d0daa34700140819b7/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6789b3d0daa34700140819b7/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6789b3d0daa34700140819b7/poster16_9-20250118000440.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}}, {"_id": "5baeb11bb061c23f203e7848", "seriesID": "5baeb11ab061c23f203e783f", "slug": "zodiac-1-1", "name": "Zodiac", "summary": "Based on the <PERSON> books about the real life notorious <PERSON><PERSON><PERSON>, a serial killer who terrorized San Francisco with a string of seemingly random murders during the 1960s and 1970s.", "description": "Based on the <PERSON> books about the real-life notorious <PERSON><PERSON><PERSON>, a serial killer who terrorized San Francisco with a string of seemingly random murders during the 1960s and 1970s.", "duration": 12600000, "originalContentDuration": 9465000, "allotment": 12600, "rating": "R", "featuredImage": {"path": "https://mam-assets.clusters.pluto.tv/assets/0bc51f85-1381-4a7a-8edd-869165a11b7e.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5baeb11bb061c23f203e7848/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5baeb11bb061c23f203e7848/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5baeb11bb061c23f203e7848/poster_1751400737470.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5baeb11bb061c23f203e7848/screenshot4_3_1751400738196.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://mam-assets.clusters.pluto.tv/assets/0bc51f85-1381-4a7a-8edd-869165a11b7e.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5baeb11bb061c23f203e7848/poster16_9_1751400738323.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "originalReleaseDate": "2007-03-03T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66f71a00b99f850013dc760a", "slug": "the-good-wife-ptv2", "name": "The Good Wife", "summary": "The Good Wife stars <PERSON><PERSON> as <PERSON>, a disgraced wife who returns to work as a lawyer after her husband, <PERSON>, is imprisoned following a scandal.", "description": "The Good Wife stars <PERSON><PERSON> as <PERSON>, a disgraced wife who returns to work as a lawyer after her husband, <PERSON>, is imprisoned following a scandal.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66f71a00b99f850013dc760a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66f71a00b99f850013dc760a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66f71a00b99f850013dc760a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66f71a00b99f850013dc760a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "675783198d00c60014224baa", "slug": "long<PERSON>e", "name": "<PERSON><PERSON><PERSON>", "summary": "Based upon the <PERSON> mystery novels by <PERSON>, LONGMIRE is a contemporary crime thriller set in Big Sky country that focuses on a Wyoming sheriff rebuilding his life and career after the death of his wife.", "description": "Based upon the <PERSON> mystery novels by <PERSON>, LONGMIRE is a contemporary crime thriller set in Big Sky country that focuses on a Wyoming sheriff rebuilding his life and career after the death of his wife.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/675783198d00c60014224baa/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Western", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/675783198d00c60014224baa/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/675783198d00c60014224baa/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/675783198d00c60014224baa/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5c9c08c6c8ccd6797db67fac", "seriesID": "5c9c08c5c8ccd6797db67fa2", "slug": "vampire-in-brooklyn-1-1", "name": "Vampire in Brooklyn", "summary": "As the vampire <PERSON><PERSON><PERSON>, the popular star is the most diabolical, dashing and definitely undead dude in the ‘hood’.", "description": "As the vampire <PERSON><PERSON><PERSON>, the popular star is the most diabolical, dashing and definitely undead dude in the ‘hood’.", "duration": 8100000, "originalContentDuration": 6135000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5c9c08c6c8ccd6797db67fac/screenshot16_9_1750783006514.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5c9c08c6c8ccd6797db67fac/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5c9c08c6c8ccd6797db67fac/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5c9c08c6c8ccd6797db67fac/poster_1750783005604.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5c9c08c6c8ccd6797db67fac/screenshot4_3_1750783004944.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5c9c08c6c8ccd6797db67fac/screenshot16_9_1750783006514.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5c9c08c6c8ccd6797db67fac/poster16_9_1750783006503.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Zakes <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1995-10-26T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "679ab73006de83001463b2a3", "seriesID": "679ab72e06de83001463b27e", "slug": "monty-python-and-the-holy-grail-1975-1-1", "name": "<PERSON> and the Holy Grail", "summary": "Grossing the highest box-office of any British film in the US when released in 1975, this \"cult classic\" comedy from the Monty Python team loosely follows the legend of <PERSON>, along with his squire and his Knights of the Round Table.", "description": "Grossing the highest box-office of any British film in the US when released in 1975, this \"cult classic\" comedy from the Monty Python team loosely follows the legend of <PERSON>, along with his squire and his Knights of the Round Table.", "duration": 7200000, "originalContentDuration": 5535000, "allotment": 7200, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/679ab73006de83001463b2a3/screenshot16_9_1749511442401.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/679ab73006de83001463b2a3/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/679ab73006de83001463b2a3/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/679ab73006de83001463b2a3/poster_1749511442916.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/679ab73006de83001463b2a3/screenshot4_3_1749516814218.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/679ab73006de83001463b2a3/screenshot16_9_1749511442401.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/679ab73006de83001463b2a3/poster16_9_1749511442920.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["en"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1975-03-14T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5d4a1d375e0db5822c3e9c0c", "seriesID": "5d4a1d345e0db5822c3e9c05", "slug": "boondock-saints-1998-1-1", "name": "The Boondock Saints", "summary": "Two Irish Catholic brothers become vigilantes and wipe out Boston's criminal underworld in the name of <PERSON>.", "description": "Two Irish Catholic brothers become vigilantes and wipe out Boston's criminal underworld in the name of <PERSON>.", "duration": 9000000, "originalContentDuration": 6510000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5d4a1d375e0db5822c3e9c0c/screenshot16_9_1749132753547.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5d4a1d375e0db5822c3e9c0c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5d4a1d375e0db5822c3e9c0c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5d4a1d375e0db5822c3e9c0c/poster_1749132752829.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5d4a1d375e0db5822c3e9c0c/screenshot4_3_1749132747613.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5d4a1d375e0db5822c3e9c0c/screenshot16_9_1749132753547.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5d4a1d375e0db5822c3e9c0c/poster16_9_1749132750936.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1999-01-22T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "67d2f5cb6baaac5c46ff5b8b", "seriesID": "67d2f5c96baaac5c46ff5b64", "slug": "the-commitments-1991-1-1", "name": "The Commitments", "summary": "<PERSON>, an unemployed Dublin boy, decides to put together a soul band made up entirely of the Irish working class.", "description": "<PERSON>, an unemployed Dublin boy, decides to put together a soul band made up entirely of the Irish working class.", "duration": 9000000, "originalContentDuration": 7080000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/67d2f5cb6baaac5c46ff5b8b/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Music", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/67d2f5cb6baaac5c46ff5b8b/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/67d2f5cb6baaac5c46ff5b8b/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/67d2f5cb6baaac5c46ff5b8b/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/67d2f5cb6baaac5c46ff5b8b/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/67d2f5cb6baaac5c46ff5b8b/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/67d2f5cb6baaac5c46ff5b8b/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1991-09-13T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66d0bb64a1c89200137fb0e6", "slug": "rizzoli-and-isles", "name": "Rizzoli & Isles", "summary": "Boston detective <PERSON> is pulled into a case involving a serial killer. <PERSON> is tough, and medical examiner <PERSON><PERSON> is methodical and blunt. <PERSON><PERSON><PERSON><PERSON> and <PERSON> are bonded by their dedication to work and their strong friendship.", "description": "Boston detective <PERSON> is pulled into a case involving a serial killer. <PERSON> is tough, and medical examiner <PERSON><PERSON> is methodical and blunt. <PERSON><PERSON><PERSON><PERSON> and <PERSON> are bonded by their dedication to work and their strong friendship.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66d0bb64a1c89200137fb0e6/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66d0bb64a1c89200137fb0e6/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66d0bb64a1c89200137fb0e6/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66d0bb64a1c89200137fb0e6/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "65e90a8bea323e0013cb87f6", "seriesID": "65e90a88ea323e0013cb87d1", "slug": "all-styles-2019-1-1", "name": "All Styles", "summary": "<PERSON>, a freshman in college, tries to focus on his studies but keeps coming to the same conclusion, dance is his passion. He assembles a ragtag crew to face off against his former crew and must prove that they have what it takes to make it as dancers.", "description": "<PERSON>, a freshman in college, tries to focus on his studies but keeps coming to the same conclusion, dance is his passion. He assembles a ragtag crew to face off against his former crew and must prove that they have what it takes to make it as dancers.", "duration": 7200000, "originalContentDuration": 5445000, "allotment": 7200, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/65e90a8bea323e0013cb87f6/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/65e90a8bea323e0013cb87f6/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/65e90a8bea323e0013cb87f6/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/65e90a8bea323e0013cb87f6/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/65e90a8bea323e0013cb87f6/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/65e90a8bea323e0013cb87f6/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/65e90a8bea323e0013cb87f6/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON>.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2018-08-18T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "667ca74a1e15340013bf1952", "seriesID": "667ca7481e15340013bf192f", "slug": "jane-got-a-gun-2016-1-1-ptv1", "name": "<PERSON> Got a Gun", "summary": "A woman asks her ex-lover for help in order to save her outlaw husband from a gang out to kill him.", "description": "A woman asks her ex-lover for help in order to save her outlaw husband from a gang out to kill him.", "duration": 7200000, "originalContentDuration": 5880000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/667ca74a1e15340013bf1952/screenshot16_9_1748377443193.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Western", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/667ca74a1e15340013bf1952/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/667ca74a1e15340013bf1952/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/667ca74a1e15340013bf1952/poster_1748377443163.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/667ca74a1e15340013bf1952/screenshot4_3_1748377443323.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/667ca74a1e15340013bf1952/screenshot16_9_1748377443193.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/667ca74a1e15340013bf1952/poster16_9_1748377442991.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "2015-12-31T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "63337f1005b8af0013854419", "seriesID": "63337f0d05b8af0013854410", "slug": "hesher-samuel-goldwyn-2011-1-1", "name": "<PERSON><PERSON>", "summary": "A young boy has lost his mother and is losing touch with his father.", "description": "A young boy has lost his mother and is losing touch with his father.", "duration": 8100000, "originalContentDuration": 6345000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/63337f1005b8af0013854419/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/63337f1005b8af0013854419/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/63337f1005b8af0013854419/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/63337f1005b8af0013854419/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/63337f1005b8af0013854419/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/63337f1005b8af0013854419/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/63337f1005b8af0013854419/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2010-01-22T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5d695a73224dcd988e0a7112", "seriesID": "5d695a72224dcd988e0a710b", "slug": "filly-brown-2013-1-1", "name": "<PERSON><PERSON>", "summary": "A promising hip-hop rhymer from Los Angeles finds herself in a gray area when a record producer offers her a compromising shot at stardom.", "description": "A promising hip-hop rhymer from Los Angeles finds herself in a gray area when a record producer offers her a compromising shot at stardom.", "duration": 8100000, "originalContentDuration": 6135000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5d695a73224dcd988e0a7112/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Music", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5d695a73224dcd988e0a7112/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5d695a73224dcd988e0a7112/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5d695a73224dcd988e0a7112/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5d695a73224dcd988e0a7112/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5d695a73224dcd988e0a7112/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5d695a73224dcd988e0a7112/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON><PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "2013-04-19T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "65df89ba92556900130fea21", "seriesID": "65df89b892556900130fe9fc", "slug": "showdown-at-the-grand-2023-1-1", "name": "Showdown at the Grand", "summary": "A proud movie theater owner must defend his family business from corporate developers alongside a legendary action star as art imitates life in a showdown for the ages.", "description": "A proud movie theater owner must defend his family business from corporate developers alongside a legendary action star as art imitates life in a showdown for the ages.", "duration": 7200000, "originalContentDuration": 5550000, "allotment": 7200, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/episodes/65df89ba92556900130fea21/screenshot16_9_1749160802129.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Crime", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/65df89ba92556900130fea21/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/65df89ba92556900130fea21/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/65df89ba92556900130fea21/poster_1749160802448.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/65df89ba92556900130fea21/screenshot4_3_1749160802462.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/65df89ba92556900130fea21/screenshot16_9_1749160802129.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/65df89ba92556900130fea21/poster16_9_1749160827997.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Piper Curda"], "directors": ["<PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2023-11-10T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "63daf1c0ff32c10013d73240", "slug": "star-trek-voyager", "name": "Star Trek: Voyager", "summary": "The legend continues with Star Trek: Voyager, the newest chapter in the franchise. Catapulted into the distant sector of the galaxy, the Federation's first female captain and her crew encounter strange new worlds in their quest to return home.", "description": "The legend continues with Star Trek: Voyager, the newest chapter in the franchise. Catapulted into the distant sector of the galaxy, the Federation's first female captain and her crew encounter strange new worlds in their quest to return home.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/63daf1c0ff32c10013d73240/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Sci-Fi & Fantasy", "type": "series", "seasonsNumbers": [2], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/63daf1c0ff32c10013d73240/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/63daf1c0ff32c10013d73240/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/63daf1c0ff32c10013d73240/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "66bcec6e5b669a00138015cd", "seriesID": "66bcec6c5b669a00138015a7", "slug": "faceless-after-dark-2024-1-1", "name": "Faceless After Dark", "summary": "Following her breakout success as the star of a killer clown horror flick, <PERSON> now finds herself struggling to capitalize on its success. But when she is held hostage by an unhinged fan posing as that same killer clown, horror becomes her reality.", "description": "Following her breakout success as the star of a killer clown horror flick, <PERSON> now finds herself struggling to capitalize on its success. But when she is held hostage by an unhinged fan posing as that same killer clown, horror becomes her reality.", "duration": 6300000, "originalContentDuration": 4980000, "allotment": 6300, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/episodes/66bcec6e5b669a00138015cd/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/66bcec6e5b669a00138015cd/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/66bcec6e5b669a00138015cd/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/66bcec6e5b669a00138015cd/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/66bcec6e5b669a00138015cd/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/66bcec6e5b669a00138015cd/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "ratingDescriptors": ["us-v", "us-l"], "poster16_9": {"path": "https://images.pluto.tv/episodes/66bcec6e5b669a00138015cd/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Israel Vaughn"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "2024-05-17T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "621e700be17acd00139cab83", "seriesID": "621e7009e17acd00139cab7c", "slug": "accident-man-2017-1-1", "name": "Accident Man", "summary": "<PERSON> is a tough hitman, who makes his hits look like accidents or suicide. He's in a gang of hitmen, each with his own style. When his loved ex is killed, <PERSON> looks for those responsible.", "description": "<PERSON> is a tough hitman, who makes his hits look like accidents or suicide. He's in a gang of hitmen, each with his own style. When his loved ex is killed, <PERSON> looks for those responsible.", "duration": 8100000, "originalContentDuration": 6345000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/621e700be17acd00139cab83/screenshot16_9_1744930447675.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/621e700be17acd00139cab83/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/621e700be17acd00139cab83/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/621e700be17acd00139cab83/poster_1744930446112.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/621e700be17acd00139cab83/screenshot4_3_1744930448691.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/621e700be17acd00139cab83/screenshot16_9_1744930447675.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/621e700be17acd00139cab83/poster16_9_1744930443978.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "Ray <PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2018-02-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "676203dad947430013281143", "seriesID": "676203d8d94743001328111d", "slug": "succubus-2024-1-1", "name": "Succ<PERSON>us", "summary": "A new father struggling with fatigue, emotional insecurities, and a failing marriage joins a dating app, only to swipe right on what may be an inhuman presence.", "description": "A new father struggling with fatigue, emotional insecurities, and a failing marriage joins a dating app, only to swipe right on what may be an inhuman presence.", "duration": 8100000, "originalContentDuration": 6195000, "allotment": 8100, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/episodes/676203dad947430013281143/screenshot16_9_1749162241495.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/676203dad947430013281143/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/676203dad947430013281143/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/676203dad947430013281143/poster_1749162241285.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/676203dad947430013281143/screenshot4_3_1749162241605.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/676203dad947430013281143/screenshot16_9_1749162241495.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/676203dad947430013281143/poster16_9_1749162241479.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON><PERSON><PERSON><PERSON>"], "originalReleaseDate": "2024-09-24T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "671998a8e87eac0013d81e8f", "slug": "festive-fireplace-ptv5", "name": "Festive Fireplace", "summary": "Cozy up to the special edition fireplace, just in time for the Holidays. The soft crackling of burning wood will turn your home into a calming retreat.", "description": "Cozy up to the special edition fireplace, just in time for the Holidays. The soft crackling of burning wood will turn your home into a calming retreat.", "originalContentDuration": 0, "rating": "TV-G", "featuredImage": {"path": "https://images.pluto.tv/series/671998a8e87eac0013d81e8f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Home & Lifestyle", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/671998a8e87eac0013d81e8f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/671998a8e87eac0013d81e8f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/671998a8e87eac0013d81e8f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "671998aae87eac0013d81ed5", "slug": "crackling-fireplace", "name": "Crackling Fireplace", "summary": "Cozy up to the special edition fireplace, just in time for the Holidays. The soft crackling of burning wood will turn your home into a calming retreat.", "description": "Cozy up to the special edition fireplace, just in time for the Holidays. The soft crackling of burning wood will turn your home into a calming retreat.", "originalContentDuration": 0, "rating": "TV-G", "featuredImage": {"path": "https://images.pluto.tv/series/671998aae87eac0013d81ed5/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Home & Lifestyle", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/671998aae87eac0013d81ed5/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/671998aae87eac0013d81ed5/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/671998aae87eac0013d81ed5/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "67466f103b8ac600134bab44", "slug": "the-drew-carey-show", "name": "The <PERSON>", "summary": "A <PERSON> comedy about an average guy who won't be hired to model <PERSON><PERSON> but will bring lots of laughs.", "description": "A <PERSON> comedy about an average guy who won't be hired to model <PERSON><PERSON> but will bring lots of laughs.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/67466f103b8ac600134bab44/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/67466f103b8ac600134bab44/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/67466f103b8ac600134bab44/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/67466f103b8ac600134bab44/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6743cc102678a5001467e6c7", "slug": "perfect-strangers", "name": "Perfect Strangers", "summary": "A Mediterranean immigrant comes to live with his distantly related American cousin, creating a culture clash.", "description": "A Mediterranean immigrant comes to live with his distantly related American cousin, creating a culture clash.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/6743cc102678a5001467e6c7/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6743cc102678a5001467e6c7/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6743cc102678a5001467e6c7/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6743cc102678a5001467e6c7/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "667f3eaede2358001312a072", "slug": "swat-sony", "name": "S.W.A.T.", "summary": "Follows a locally born and bred S.W.A.T. lieutenant who is torn between loyalty to the streets and duty to his fellow officers when he's tasked to run a highly-trained unit that's the last stop for solving crimes in Los Angeles.", "description": "Follows a locally born and bred S.W.A.T. lieutenant who is torn between loyalty to the streets and duty to his fellow officers when he's tasked to run a highly-trained unit that's the last stop for solving crimes in Los Angeles.", "originalContentDuration": 0, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/series/667f3eaede2358001312a072/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/667f3eaede2358001312a072/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/667f3eaede2358001312a072/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/667f3eaede2358001312a072/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6744ad1071cc440013b9274e", "slug": "growing-pains", "name": "Growing Pains", "summary": "Fatherhood has taken on a new meaning for <PERSON>, who has begun cooking, cleaning and minding the kids.", "description": "Fatherhood has taken on a new meaning for <PERSON>, who has begun cooking, cleaning and minding the kids.", "originalContentDuration": 0, "rating": "TV-G", "featuredImage": {"path": "https://images.pluto.tv/series/6744ad1071cc440013b9274e/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6744ad1071cc440013b9274e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6744ad1071cc440013b9274e/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6744ad1071cc440013b9274e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5d434b08c4a19d3369dc777c", "slug": "walker-texas-ranger", "name": "<PERSON>, Texas Ranger", "summary": "<PERSON> and his partner <PERSON><PERSON><PERSON> are Texas Rangers. They make it their business to battle crime in Dallas and all around the Great State of Texas.", "description": "<PERSON> and his partner <PERSON><PERSON><PERSON> are Texas Rangers. They make it their business to battle crime in Dallas and all around the Great State of Texas.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5d434b08c4a19d3369dc777c/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Action & Adventure", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5d434b08c4a19d3369dc777c/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5d434b08c4a19d3369dc777c/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5d434b08c4a19d3369dc777c/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6712f0e6b99f85001303cd68", "slug": "ncis-origins-ptv1", "name": "NCIS: Origins", "summary": "The story he never told— journey back to the origins of <PERSON>.", "description": "The story he never told— journey back to the origins of <PERSON>.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/6712f0e6b99f85001303cd68/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6712f0e6b99f85001303cd68/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6712f0e6b99f85001303cd68/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6712f0e6b99f85001303cd68/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "60bf9f6bea81e400076a2c33", "slug": "ncis-cbs-rolling-4-ptv", "name": "NCIS", "summary": "NCIS follows Special Agent <PERSON> and his unique team of investigators as they work together through their complex and always amusing dynamics while trying to solve mysteries and work together in high-stress situations.", "description": "NCIS follows Special Agent <PERSON> and his unique team of investigators as they work together through their complex and always amusing dynamics while trying to solve mysteries and work together in high-stress situations.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/60bf9f6bea81e400076a2c33/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [22], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/60bf9f6bea81e400076a2c33/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/60bf9f6bea81e400076a2c33/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/60bf9f6bea81e400076a2c33/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "65ce4e5003fa740013793127", "slug": "tracker-ptv3", "name": "Tracker", "summary": "The new CBS series Tracker stars <PERSON> as <PERSON><PERSON>, a mysterious reward seeker and expert tracker, based on the novel \"The Never Game\".", "description": "The new CBS series Tracker stars <PERSON> as <PERSON><PERSON>, a mysterious reward seeker and expert tracker, based on the novel \"The Never Game\".", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/65ce4e5003fa740013793127/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [2], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/65ce4e5003fa740013793127/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/65ce4e5003fa740013793127/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/65ce4e5003fa740013793127/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "66d628b86ec6ad0013614d0c", "slug": "maverick", "name": "Ma<PERSON><PERSON>", "summary": "<PERSON><PERSON>, his brother <PERSON> and cousin <PERSON> -- three handsome bachelor cardsharps in the Wild West -- would just as soon slip out of town quietly as face a gunman. But when it's time for a showdown, don't bet against a Maverick.", "description": "<PERSON><PERSON>, his brother <PERSON> and cousin <PERSON> -- three handsome bachelor cardsharps in the Wild West -- would just as soon slip out of town quietly as face a gunman. But when it's time for a showdown, don't bet against a Maverick.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66d628b86ec6ad0013614d0c/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Western", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66d628b86ec6ad0013614d0c/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66d628b86ec6ad0013614d0c/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66d628b86ec6ad0013614d0c/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "66e3b328e0873100130f76f3", "slug": "kung-fu", "name": "Kung Fu", "summary": "<PERSON> stars as a Buddhist monk and hunted man who wanders the American West in the 1870s fighting intolerance and injustice with his mastery of an ancient form of high combat known as Kung Fu.", "description": "<PERSON> stars as a Buddhist monk and hunted man who wanders the American West in the 1870s fighting intolerance and injustice with his mastery of an ancient form of high combat known as Kung Fu.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/66e3b328e0873100130f76f3/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66e3b328e0873100130f76f3/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66e3b328e0873100130f76f3/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66e3b328e0873100130f76f3/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "667b0e10946b3600134336dc", "slug": "the-new-adventures-of-old-christine", "name": "The New Adventures of Old Christine", "summary": "Divorced working mom <PERSON> successfully juggles the daily stresses. But <PERSON>'s life got more complicated with her ex-husband <PERSON>'s new girlfriend around: a young, impossible-to-hate woman also named <PERSON>… the \"new\" Christine.", "description": "Divorced working mom <PERSON> successfully juggles the daily stresses. But <PERSON>'s life got more complicated with her ex-husband <PERSON>'s new girlfriend around: a young, impossible-to-hate woman also named <PERSON>… the \"new\" Christine.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/667b0e10946b3600134336dc/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/667b0e10946b3600134336dc/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/667b0e10946b3600134336dc/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/667b0e10946b3600134336dc/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "666782811c028200130e06cf", "slug": "big-brother-cbs-tv", "name": "Big Brother", "summary": "BIG BROTHER follows a group of people living together in a house outfitted with 94 HD cameras and 113 microphones, recording their every move 24 hours a day. Each week, someone will be voted out, with the final Houseguest receiving the grand prize.", "description": "BIG BROTHER follows a group of people living together in a house outfitted with 94 HD cameras and 113 microphones, recording their every move 24 hours a day. Each week, someone will be voted out, with the final Houseguest receiving the grand prize.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/666782811c028200130e06cf/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Reality", "type": "series", "seasonsNumbers": [19, 20, 21, 22, 23, 24, 25, 26], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/666782811c028200130e06cf/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/666782811c028200130e06cf/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/666782811c028200130e06cf/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "620159f3dae983001ace02ac", "slug": "blue-bloods-cbs-tv-lf", "name": "Blue Bloods", "summary": "BLUE BLOODS is a drama about a multi-generational family of cops dedicated to New York City law enforcement. <PERSON> is the New York Police Commissioner and heads both the police force and the <PERSON> brood.", "description": "BLUE BLOODS is a drama about a multi-generational family of cops dedicated to New York City law enforcement. <PERSON> is the New York Police Commissioner and heads both the police force and the <PERSON> brood.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/620159f3dae983001ace02ac/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [4, 5, 6, 7, 8, 9, 10], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/620159f3dae983001ace02ac/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/620159f3dae983001ace02ac/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/620159f3dae983001ace02ac/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "66959a7897009300135c0e7d", "slug": "without-a-trace", "name": "Without A Trace", "summary": "Without a Trace provides a fascinating glimpse into the search for people who vanish inexplicably. For FBI agent <PERSON> and his colleagues, finding the missing depends as much on figuring out who they are as it does on determining where they are.", "description": "Without a Trace provides a fascinating glimpse into the search for people who vanish inexplicably. For FBI agent <PERSON> and his colleagues, finding the missing depends as much on figuring out who they are as it does on determining where they are.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66959a7897009300135c0e7d/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66959a7897009300135c0e7d/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66959a7897009300135c0e7d/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66959a7897009300135c0e7d/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "66bd10a994a1840013620c7f", "slug": "the-neighborhood-cbs-tv", "name": "The Neighborhood", "summary": "The Neighborhood stars <PERSON><PERSON> the Entertainer in a comedy about what happens when the friendliest guy in the Midwest moves his family to a neighborhood in Los Angeles where not everyone looks like him or appreciates his extreme neighborliness.", "description": "The Neighborhood stars <PERSON><PERSON> the Entertainer in a comedy about what happens when the friendliest guy in the Midwest moves his family to a neighborhood in Los Angeles where not everyone looks like him or appreciates his extreme neighborliness.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/66bd10a994a1840013620c7f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/66bd10a994a1840013620c7f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/66bd10a994a1840013620c7f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/66bd10a994a1840013620c7f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6139076231d18b001487705a", "slug": "charmed", "name": "Charmed", "summary": "Witches can't run away from their legacy, at least not the <PERSON><PERSON><PERSON> sisters -- <PERSON><PERSON> (<PERSON><PERSON>), <PERSON> (<PERSON>) and <PERSON> (<PERSON><PERSON>).", "description": "Witches can't run away from their legacy, at least not the <PERSON><PERSON><PERSON> sisters -- <PERSON><PERSON> (<PERSON><PERSON>), <PERSON> (<PERSON>) and <PERSON> (<PERSON><PERSON>).", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/6139076231d18b001487705a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Sci-Fi & Fantasy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6139076231d18b001487705a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6139076231d18b001487705a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6139076231d18b001487705a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "655515303e6938001a1451b2", "slug": "swamp-murders", "name": "Swamp Murders", "summary": "Investigators reveal the stories behind bodies found in swamps.", "description": "Investigators reveal the stories behind bodies found in swamps.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/655515303e6938001a1451b2/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Crime", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/655515303e6938001a1451b2/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/655515303e6938001a1451b2/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/655515303e6938001a1451b2/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6557c0b0c347d500134f9feb", "slug": "southern-fried-homicide", "name": "Southern Fried Homicide", "summary": "Classic true crime is served up against a backdrop of Southern hospitality.", "description": "Classic true crime is served up against a backdrop of Southern hospitality.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/6557c0b0c347d500134f9feb/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Crime", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6557c0b0c347d500134f9feb/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6557c0b0c347d500134f9feb/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6557c0b0c347d500134f9feb/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5f875e9e7ecf48001a33e290", "slug": "vegas-rat-rods", "name": "Vegas Rat Rods", "summary": "<PERSON> builds custom, mind-blowing rad rods in Las Vegas, Nevada.", "description": "<PERSON> builds custom, mind-blowing rad rods in Las Vegas, Nevada.", "originalContentDuration": 0, "rating": "Not Rated", "featuredImage": {"path": "https://images.pluto.tv/series/5f875e9e7ecf48001a33e290/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [2, 3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5f875e9e7ecf48001a33e290/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5f875e9e7ecf48001a33e290/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5f875e9e7ecf48001a33e290/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5bf446e27788b308749c4761", "slug": "man-vs-wild", "name": "Man vs. Wild", "summary": "<PERSON> trained as a survival expert with the British Special Forces. Follow him through some of the most unforgiving terrain in the world, as he does everything necessary to survive.", "description": "<PERSON> trained as a survival expert with the British Special Forces. Follow him through some of the most unforgiving terrain in the world, as he does everything necessary to survive.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/5bf446e27788b308749c4761/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5bf446e27788b308749c4761/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5bf446e27788b308749c4761/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5bf446e27788b308749c4761/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5cabe063f3e11bfe03a78aee", "slug": "fatal-attractions", "name": "Fatal Attractions", "summary": "Fatal Attractions features people who bring deadly animals into their homes due to obsession, loneliness and desire. This gripping series examines the most extraordinary pet attacks of recent years - incidents in which people living with tigers.", "description": "Fatal Attractions features people who bring deadly animals into their homes due to obsession, loneliness and desire. This gripping series examines the most extraordinary pet attacks of recent years - incidents in which people living with tigers.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5cabe063f3e11bfe03a78aee/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5cabe063f3e11bfe03a78aee/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5cabe063f3e11bfe03a78aee/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5cabe063f3e11bfe03a78aee/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5f877770b06bf7001adc59be", "slug": "i-was-prey", "name": "I Was <PERSON>y", "summary": "Survivors recount the true stories of confronting a deadly predator.", "description": "Survivors recount the true stories of confronting a deadly predator.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5f877770b06bf7001adc59be/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5f877770b06bf7001adc59be/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5f877770b06bf7001adc59be/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5f877770b06bf7001adc59be/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5f8486e672d139001a196fba", "slug": "dual-survival", "name": "Dual Survival", "summary": "Meet military-trained <PERSON> and naturalist <PERSON> – trained survival experts featured in Dual Survival.", "description": "Meet military-trained <PERSON> and naturalist <PERSON> – trained survival experts featured in Dual Survival.", "originalContentDuration": 0, "rating": "Not Rated", "featuredImage": {"path": "https://images.pluto.tv/series/5f8486e672d139001a196fba/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Reality", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5f8486e672d139001a196fba/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5f8486e672d139001a196fba/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5f8486e672d139001a196fba/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "61799cff6e3813001a8d6451", "slug": "roc-warner-bros", "name": "Roc", "summary": "<PERSON><PERSON>, a city garbage collector, balances the pressures of work with the everyday crises of family life in an effort to do what he thinks is best for his wife and kids.", "description": "<PERSON><PERSON>, a city garbage collector, balances the pressures of work with the everyday crises of family life in an effort to do what he thinks is best for his wife and kids.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/61799cff6e3813001a8d6451/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/61799cff6e3813001a8d6451/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/61799cff6e3813001a8d6451/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/61799cff6e3813001a8d6451/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5d4a1e8c1aedf983940fb64f", "slug": "killer-instinct-with-chris-hansen", "name": "Killer Instinct with <PERSON>", "summary": "Award-winning investigative journalist <PERSON> brings his unrivalled journalistic skills and criminal insight to Killer Instinct with <PERSON> to reveal some of America's most shocking murders.", "description": "Award-winning investigative journalist <PERSON> brings his unrivalled journalistic skills and criminal insight to Killer Instinct with <PERSON> to reveal some of America's most shocking murders.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/5d4a1e8c1aedf983940fb64f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5d4a1e8c1aedf983940fb64f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5d4a1e8c1aedf983940fb64f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5d4a1e8c1aedf983940fb64f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "65ce5c60a3a8580013c4b64a", "slug": "king-of-queens", "name": "The King of Queens", "summary": "Comedy stars stand-up <PERSON> as a Queens, New York blue-collar husband who has finally found the formula to balancing a successful marriage with his lifelong neighborhood friends... that is until his wife's sister and father move in and chaos reigns.", "description": "Comedy stars stand-up <PERSON> as a Queens, New York blue-collar husband who has finally found the formula to balancing a successful marriage with his lifelong neighborhood friends... that is until his wife's sister and father move in and chaos reigns.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/65ce5c60a3a8580013c4b64a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/65ce5c60a3a8580013c4b64a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/65ce5c60a3a8580013c4b64a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/65ce5c60a3a8580013c4b64a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5d8e4e0f15e72d61b8a26729", "slug": "yukon-men", "name": "Yukon Men", "summary": "Boys grow into men quickly here because they must help the family survive. Father-son bonding and tensions take on a new meaning in this environment and this year, the odds are stacked against them.", "description": "Boys grow into men quickly here because they must help the family survive. Father-son bonding and tensions take on a new meaning in this environment and this year, the odds are stacked against them.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5d8e4e0f15e72d61b8a26729/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Reality", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5d8e4e0f15e72d61b8a26729/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5d8e4e0f15e72d61b8a26729/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5d8e4e0f15e72d61b8a26729/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "63bee6dbeb85c5001335c70e", "slug": "rules-of-engagement-ptv2", "name": "Rules of Engagement", "summary": "It doesn't matter if you're in love, out of love, or somewhere in between, you have to play by the RULES OF ENGAGEMENT!", "description": "It doesn't matter if you're in love, out of love, or somewhere in between, you have to play by the RULES OF ENGAGEMENT!", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/63bee6dbeb85c5001335c70e/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 7], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/63bee6dbeb85c5001335c70e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/63bee6dbeb85c5001335c70e/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/63bee6dbeb85c5001335c70e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6215314d5231f2001387d080", "slug": "just-shoot-me", "name": "Just Shoot Me", "summary": "Fashion and mayhem at the offices of Blush magazine, where <PERSON>, (<PERSON> of Sex, Lies, & Videotape and Pretty Woman) works for her father (<PERSON>) and a host of eccentric and neurotic co-workers.", "description": "Fashion and mayhem at the offices of Blush magazine, where <PERSON>, (<PERSON> of Sex, Lies, & Videotape and Pretty Woman) works for her father (<PERSON>) and a host of eccentric and neurotic co-workers.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/6215314d5231f2001387d080/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6215314d5231f2001387d080/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6215314d5231f2001387d080/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6215314d5231f2001387d080/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5f848ab2dada31001ad9c325", "slug": "gator-boys", "name": "Gator Boys", "summary": "The death-defying exploits of two extraordinary alligator trappers.", "description": "The death-defying exploits of two extraordinary alligator trappers.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5f848ab2dada31001ad9c325/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5f848ab2dada31001ad9c325/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5f848ab2dada31001ad9c325/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5f848ab2dada31001ad9c325/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "60da17cdb8187f00136ab21f", "slug": "supermarket-sweep", "name": "Supermarket Sweep", "summary": "Teams answer questions to earn time and advantages over their competitors before going on a supermarket shopping spree. The team that adds the most valuable items to their carts wins.", "description": "Teams answer questions to earn time and advantages over their competitors before going on a supermarket shopping spree. The team that adds the most valuable items to their carts wins.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/60da17cdb8187f00136ab21f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [2, 3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/60da17cdb8187f00136ab21f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/60da17cdb8187f00136ab21f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/60da17cdb8187f00136ab21f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "653c1990a61c2a001b912e3d", "slug": "the-streets-of-san-francisco", "name": "The Streets of San Francisco", "summary": "Twenty-year veteran Detective Lt. <PERSON> is partnered with young, college-educated Inspector <PERSON>, who has a lot to learn about being a police detective on the Streets of San Francisco.", "description": "Twenty-year veteran Detective Lt. <PERSON> is partnered with young, college-educated Inspector <PERSON>, who has a lot to learn about being a police detective on the Streets of San Francisco.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/653c1990a61c2a001b912e3d/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [4, 5], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/653c1990a61c2a001b912e3d/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/653c1990a61c2a001b912e3d/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/653c1990a61c2a001b912e3d/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "65301d20a78f0d001ace948e", "slug": "undercover-boss-ptv6", "name": "Undercover Boss", "summary": "Executives go on an undercover mission to examine the inner workings of their company.", "description": "Executives go on an undercover mission to examine the inner workings of their company.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/65301d20a78f0d001ace948e/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Reality", "type": "series", "seasonsNumbers": [5, 6, 7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/65301d20a78f0d001ace948e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/65301d20a78f0d001ace948e/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/65301d20a78f0d001ace948e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "654e9700a78f0d001ae5c4ef", "slug": "hogans-heroes", "name": "Hogan's Heroes", "summary": "The inmates of a German World War II prisoner of war camp conduct an espionage and sabotage campaign right under the noses of their warders.", "description": "The inmates of a German World War II prisoner of war camp conduct an espionage and sabotage campaign right under the noses of their warders.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/654e9700a78f0d001ae5c4ef/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/654e9700a78f0d001ae5c4ef/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/654e9700a78f0d001ae5c4ef/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/654e9700a78f0d001ae5c4ef/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "65415e288026ff001afa75d7", "slug": "mamas-family", "name": "<PERSON>'s Family", "summary": "<PERSON><PERSON> and her spinster sister <PERSON><PERSON> open their home to <PERSON><PERSON>'s recently divorced son <PERSON><PERSON> and his teenage son and daughter. It's quite an adjustment for everyone, especially the cranky, argumentative <PERSON><PERSON>.", "description": "<PERSON><PERSON> and her spinster sister <PERSON><PERSON> open their home to <PERSON><PERSON>'s recently divorced son <PERSON><PERSON> and his teenage son and daughter. It's quite an adjustment for everyone, especially the cranky, argumentative <PERSON><PERSON>.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/65415e288026ff001afa75d7/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/65415e288026ff001afa75d7/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/65415e288026ff001afa75d7/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/65415e288026ff001afa75d7/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5be464b90eee9b882f1365a0", "slug": "river-monsters", "name": "River Monsters", "summary": "In River Monsters, join host, biologist, and extreme angler <PERSON>, as he catches the extraordinary and supersized fish that lurk in our planet's rivers and lakes. Traveling the globe and risking his life, he searches for freshwater predators.", "description": "In River Monsters, join host, biologist, and extreme angler <PERSON>, as he catches the extraordinary and supersized fish that lurk in our planet's rivers and lakes. Traveling the globe and risking his life, he searches for freshwater predators.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5be464b90eee9b882f1365a0/featuredImage_1749244682344.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3, 5, 6, 7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5be464b90eee9b882f1365a0/poster_1749244682307.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5be464b90eee9b882f1365a0/tile_1749244682414.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5be464b90eee9b882f1365a0/poster16_9_1749244682175.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "60efc755d806630013d9ace9", "slug": "big-brother-cbs-rolling-4", "name": "Big Brother", "summary": "Big Brother follows a group of people living together in a house outfitted with dozens of high-definition cameras and microphones recording their every move, 24 hours a day. Each week, the Houseguests will vote someone out of the house.", "description": "Big Brother follows a group of people living together in a house outfitted with dozens of high-definition cameras and microphones recording their every move, 24 hours a day. Each week, the Houseguests will vote someone out of the house.", "originalContentDuration": 0, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/series/60efc755d806630013d9ace9/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Reality", "type": "series", "seasonsNumbers": [26], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/60efc755d806630013d9ace9/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/60efc755d806630013d9ace9/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/60efc755d806630013d9ace9/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5f079988baa45c0013a59db2", "slug": "csiny-series", "name": "CSI: NY", "summary": "CSI: NY, a crime drama, is about forensic investigators who use high-tech science to follow the evidence and solve crimes in the Big Apple.", "description": "CSI: NY, a crime drama, is about forensic investigators who use high-tech science to follow the evidence and solve crimes in the Big Apple.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/5f079988baa45c0013a59db2/featuredImage_1749165507711.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5f079988baa45c0013a59db2/poster_1749165507851.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5f079988baa45c0013a59db2/tile_1749165509422.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5f079988baa45c0013a59db2/poster16_9_1749165509099.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}}, {"_id": "65009a1cf6f05d0013677fb3", "slug": "dynasty", "name": "Dynasty", "summary": "The first season of the primetime soap begins with oil tycoon <PERSON> marrying his former secretary, <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>'s former lover, <PERSON>, returns to town and causes more problems when he tells <PERSON><PERSON><PERSON><PERSON> that he's still in love with her.", "description": "The first season of the primetime soap begins with oil tycoon <PERSON> marrying his former secretary, <PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>'s former lover, <PERSON>, returns to town and causes more problems when he tells <PERSON><PERSON><PERSON><PERSON> that he's still in love with her.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/65009a1cf6f05d0013677fb3/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Drama", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8, 9], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/65009a1cf6f05d0013677fb3/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/65009a1cf6f05d0013677fb3/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/65009a1cf6f05d0013677fb3/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6234b65ffc8de900130ab0d2", "slug": "stargate-sg-1-ptv1", "name": "Stargate SG-1", "summary": "Stargate SG-1 picks up where the blockbuster film left off as the SG-1 team sets out to explore the mysteries of the Stargate and the worlds beyond it in the pulse-pounding series which became a worldwide phenomenon and a science fiction classic.", "description": "Stargate SG-1 picks up where the blockbuster film left off as the SG-1 team sets out to explore the mysteries of the Stargate and the worlds beyond it in the pulse-pounding series which became a worldwide phenomenon and a science fiction classic.", "originalContentDuration": 0, "rating": "TV-MA", "featuredImage": {"path": "https://images.pluto.tv/series/6234b65ffc8de900130ab0d2/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Sci-Fi & Fantasy", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6234b65ffc8de900130ab0d2/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6234b65ffc8de900130ab0d2/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6234b65ffc8de900130ab0d2/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "62142bb884ef4e00138a9d99", "slug": "the-jef<PERSON><PERSON>", "name": "The Jeffersons", "summary": "His cleaning business booming, scrappy <PERSON> decides it's time to move on up to the deluxe apartments on New York's posh east side, but <PERSON> is ill-prepared for the unexpected pitfalls his new address will bring him.", "description": "His cleaning business booming, scrappy <PERSON> decides it's time to move on up to the deluxe apartments on New York's posh east side, but <PERSON> is ill-prepared for the unexpected pitfalls his new address will bring him.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/62142bb884ef4e00138a9d99/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [5, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/62142bb884ef4e00138a9d99/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/62142bb884ef4e00138a9d99/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/62142bb884ef4e00138a9d99/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6201681342817b001a9ad377", "slug": "sanford-and-son", "name": "Sanford and Son", "summary": "The misadventures of a cantankerous junk dealer and his frustrated son.", "description": "The misadventures of a cantankerous junk dealer and his frustrated son.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/6201681342817b001a9ad377/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Comedy", "type": "series", "seasonsNumbers": [3, 4], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6201681342817b001a9ad377/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6201681342817b001a9ad377/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6201681342817b001a9ad377/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "5c0079a930b5c09b4eb3105a", "slug": "finding-bigfoot", "name": "Finding Bigfoot", "summary": "In Finding Bigfoot, four members of the Bigfoot Field Research Organization (BFRO) embark on one single-minded mission - to find the elusive \"creature\" known as Bigfoot.", "description": "In Finding Bigfoot, four members of the Bigfoot Field Research Organization (BFRO) embark on one single-minded mission - to find the elusive \"creature\" known as Bigfoot.", "originalContentDuration": 0, "rating": "TV-PG", "featuredImage": {"path": "https://images.pluto.tv/series/5c0079a930b5c09b4eb3105a/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Documentary", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/5c0079a930b5c09b4eb3105a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/5c0079a930b5c09b4eb3105a/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/5c0079a930b5c09b4eb3105a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "6552b5b5a78f0d001a033b5f", "slug": "i-almost-got-away-with-it", "name": "I (Almost) Got Away With It", "summary": "Fugitives talk about life on the run and how they almost got away with it.", "description": "Fugitives talk about life on the run and how they almost got away with it.", "originalContentDuration": 0, "rating": "TV-14", "featuredImage": {"path": "https://images.pluto.tv/series/6552b5b5a78f0d001a033b5f/featuredImage.jpg?fill=blur&fit=fill&fm=jpg&h=900&q=75&w=1600"}, "genre": "Crime", "type": "series", "seasonsNumbers": [1, 2, 3, 4, 5, 6, 7, 8], "stitched": {}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/series/6552b5b5a78f0d001a033b5f/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "1:1", "url": "https://images.pluto.tv/series/6552b5b5a78f0d001a033b5f/tile.jpg?fill=blur&fit=fill&fm=jpg&h=660&q=75&w=660"}], "poster16_9": {"path": "https://images.pluto.tv/series/6552b5b5a78f0d001a033b5f/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "avail": {}}, {"_id": "679d32d09a098f0014fae83e", "seriesID": "679d32ce9a098f0014fae819", "slug": "monty-python-live-at-the-hollywood-bowl-1982-1-1", "name": "<PERSON> Live at the Hollywood Bowl", "summary": "The legendary live show from the Monty Python troupe. Performing many of their greatest sketches from <PERSON>'s Flying Circus and two Monty Python specials, <PERSON>'s Fliegen<PERSON> Zirkus, live from the Hollywood Bowl in 1980.", "description": "The legendary live show from the Monty Python troupe. Performing many of their greatest sketches from <PERSON>'s Flying Circus and two Monty Python specials, <PERSON>'s Fliegen<PERSON> Zirkus, live from the Hollywood Bowl in 1980.", "duration": 6300000, "originalContentDuration": 4860000, "allotment": 6300, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/679d32d09a098f0014fae83e/screenshot16_9_1749511862733.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/679d32d09a098f0014fae83e/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/679d32d09a098f0014fae83e/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/679d32d09a098f0014fae83e/poster_1749511860078.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/679d32d09a098f0014fae83e/screenshot4_3_1749511862028.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/679d32d09a098f0014fae83e/screenshot16_9_1749511862733.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/679d32d09a098f0014fae83e/poster16_9_1749511862300.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1982-06-25T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5fcefb6dce9667001a2a1262", "seriesID": "5fcefb6cce9667001a2a1256", "slug": "from-dusk-till-dawn-2-texas-blood-money-1999-1-1", "name": "From Dusk Till Dawn 2: Texas Blood Money", "summary": "When a gang of bank robbers makes a run for the border, they wander into the wrong bar and mess with the wrong vampires.  It's kill-or-be-killed in the ultimate battle to stop these evil creatures and save their own lives!", "description": "When a gang of bank robbers makes a run for the border, they wander into the wrong bar and mess with the wrong vampires.  It's kill-or-be-killed in the ultimate battle to stop these evil creatures and save their own lives!", "duration": 7200000, "originalContentDuration": 5310000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5fcefb6dce9667001a2a1262/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5fcefb6dce9667001a2a1262/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5fcefb6dce9667001a2a1262/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5fcefb6dce9667001a2a1262/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5fcefb6dce9667001a2a1262/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5fcefb6dce9667001a2a1262/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5fcefb6dce9667001a2a1262/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "1999-03-16T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5fcf0a935fc7e1001a3c11f9", "seriesID": "5fcf0a915fc7e1001a3c11ed", "slug": "from-dusk-till-dawn-3-hangmans-daughter-2000-1-1", "name": "From Dusk Till Dawn 3: <PERSON><PERSON>'s Daughter", "summary": "Set in Mexico in the early 1900s, this prequel to the From Dusk Till Dawn series finds outlaw <PERSON> on the run from the hangman, with the hangman's daughter <PERSON><PERSON><PERSON><PERSON> by his side. Together they are faced with the fight of their lives.", "description": "Set in Mexico in the early 1900s, this prequel to the From Dusk Till Dawn series finds outlaw <PERSON> on the run from the hangman, with the hangman's daughter <PERSON><PERSON><PERSON><PERSON> by his side. Together they are faced with the fight of their lives.", "duration": 7200000, "originalContentDuration": 5655000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5fcf0a935fc7e1001a3c11f9/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5fcf0a935fc7e1001a3c11f9/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5fcf0a935fc7e1001a3c11f9/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5fcf0a935fc7e1001a3c11f9/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5fcf0a935fc7e1001a3c11f9/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5fcf0a935fc7e1001a3c11f9/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5fcf0a935fc7e1001a3c11f9/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON><PERSON>"], "writers": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "1999-12-21T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5f2099077d5a3f001a490131", "seriesID": "5f2099057d5a3f001a490127", "slug": "harlem-nights-1989-1-1", "name": "Harlem Nights", "summary": "In the waning days of Prohibition, <PERSON> and his adopted son, <PERSON>, run a speakeasy called Club Sugar Ray. Gangster <PERSON><PERSON> pays corrupt cop <PERSON> to close Club Sugar Ray down.", "description": "In the waning days of Prohibition, <PERSON> and his adopted son, <PERSON>, run a speakeasy called Club Sugar Ray. Gangster <PERSON><PERSON> pays corrupt cop <PERSON> to close Club Sugar Ray down.", "duration": 9000000, "originalContentDuration": 6945000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5f2099077d5a3f001a490131/screenshot16_9_1751437645770.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5f2099077d5a3f001a490131/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5f2099077d5a3f001a490131/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5f2099077d5a3f001a490131/poster_1751437646555.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://mam-assets.clusters.pluto.tv/assets/f9b86e6c-9f9c-4439-adba-652c95e9b6a3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5f2099077d5a3f001a490131/screenshot16_9_1751437645770.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5f2099077d5a3f001a490131/poster16_9_1751437645746.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "originalReleaseDate": "1989-11-17T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5ea1a8171ea4a6001a5cdc5c", "seriesID": "5ea1a8141ea4a6001a5cdc53", "slug": "stephen-kings-graveyard-shift-1989-1-1", "name": "Stephen <PERSON>'s Graveyard Shift", "summary": "When a mill is reopened, several employees meet their deaths. The killings all occur between 11 p.m. and 7 a.m. the Graveyard Shift. Soon, the workers find a maze of tunnels leading to the cemetery and the horror that comes alive at night.", "description": "When a mill is reopened, several employees meet their deaths. The killings all occur between 11 p.m. and 7 a.m. the Graveyard Shift. Soon, the workers find a maze of tunnels leading to the cemetery and the horror that comes alive at night.", "duration": 6300000, "originalContentDuration": 5190000, "allotment": 6300, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5ea1a8171ea4a6001a5cdc5c/screenshot16_9_1751993643255.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5ea1a8171ea4a6001a5cdc5c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5ea1a8171ea4a6001a5cdc5c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5ea1a8171ea4a6001a5cdc5c/poster_1751993643131.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5ea1a8171ea4a6001a5cdc5c/screenshot4_3_1751993642944.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5ea1a8171ea4a6001a5cdc5c/screenshot16_9_1751993643255.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5ea1a8171ea4a6001a5cdc5c/poster16_9_1751993643534.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1990-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5955aba5491f7ff12f598ce0", "seriesID": "568f00cc6fdbc686673c90b8", "slug": "stephen-kings-silver-bullet-ptv4", "name": "<PERSON>'s Silver Bullet", "summary": "Something is killing off townsfolk in Ta<PERSON>er's Mills. Something ingenious. Something remotely human. But the only person in town with courage to stop this lurking menace is a 13-year-old boy, confined to a wheelchair since birth.", "description": "Something is killing off townsfolk in Ta<PERSON>er's Mills. Something ingenious. Something remotely human. But the only person in town with courage to stop this lurking menace is a 13-year-old boy, confined to a wheelchair since birth.", "duration": 7200000, "originalContentDuration": 5700000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5955aba5491f7ff12f598ce0/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5955aba5491f7ff12f598ce0/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5955aba5491f7ff12f598ce0/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5955aba5491f7ff12f598ce0/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5955aba5491f7ff12f598ce0/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5955aba5491f7ff12f598ce0/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5955aba5491f7ff12f598ce0/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Follows", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1985-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "568f00cc6fdbc686673c910a", "seriesID": "568f00cc6fdbc686673c90ba", "slug": "stephen-kings-thinner-1996-1-1", "name": "<PERSON>'s Thinner", "summary": "An obese attorney accidentally runs down and kills an old gypsy woman. After her ancient father puts a curse on him, he starts losing weight, and cannot stop. Based on the <PERSON> novel, written under the pseudonym <PERSON>.", "description": "An obese attorney accidentally runs down and kills an old gypsy woman. After her ancient father puts a curse on him, he starts losing weight, and cannot stop. Based on the <PERSON> novel, written under the pseudonym <PERSON>.", "duration": 7200000, "originalContentDuration": 5550000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/568f00cc6fdbc686673c910a/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/568f00cc6fdbc686673c910a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/568f00cc6fdbc686673c910a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/568f00cc6fdbc686673c910a/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/568f00cc6fdbc686673c910a/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/568f00cc6fdbc686673c910a/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/568f00cc6fdbc686673c910a/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1996-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5f7631e976c54900134c62a7", "seriesID": "5f7631e176c54900134c62a0", "slug": "the-faculty-1998-1-1", "name": "The Faculty", "summary": "A group of small-town high school students discovers their teachers are under the control of mind-controlling alien parasites. From a smart script by <PERSON> (SCREAM), and directed by action-expert <PERSON> (FROM DUSK TO DAWN).", "description": "A group of small-town high school students discovers their teachers are under the control of mind-controlling alien parasites. From a smart script by <PERSON> (SCREAM), and directed by action-expert <PERSON> (FROM DUSK TO DAWN).", "duration": 8100000, "originalContentDuration": 6285000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5f7631e976c54900134c62a7/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Horror", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5f7631e976c54900134c62a7/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5f7631e976c54900134c62a7/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5f7631e976c54900134c62a7/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5f7631e976c54900134c62a7/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5f7631e976c54900134c62a7/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5f7631e976c54900134c62a7/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1998-12-25T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5fb85a63e8b7e6001a754d40", "seriesID": "5fb85a62e8b7e6001a754d39", "slug": "clueless-1995-1-1", "name": "Clueless", "summary": "A US West Coast teen lifestyle parody centered around <PERSON><PERSON>, a popular high school girl who spends her days playing match-maker, helping her friends with fashion choices, and looking for a boyfriend.", "description": "A US West Coast teen lifestyle parody centered around <PERSON><PERSON>, a popular high school girl who spends her days playing match-maker, helping her friends with fashion choices, and looking for a boyfriend.", "duration": 8100000, "originalContentDuration": 5835000, "allotment": 8100, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5fb85a63e8b7e6001a754d40/screenshot16_9_1747325881274.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5fb85a63e8b7e6001a754d40/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5fb85a63e8b7e6001a754d40/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5fb85a63e8b7e6001a754d40/poster_1747325882837.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5fb85a63e8b7e6001a754d40/screenshot4_3_1747325883776.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5fb85a63e8b7e6001a754d40/screenshot16_9_1747325881274.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5fb85a63e8b7e6001a754d40/poster16_9_1747325883843.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"originalReleaseDate": "1995-07-19T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5a9dd73dfb6f2f17481aff11", "seriesID": "5a9dd73dfb6f2f17481aff0c", "slug": "lara-croft-tomb-raider-2001-1-1", "name": "<PERSON>: <PERSON> Raider", "summary": "Exploring lost empires and finding priceless treasures... it's all in a day's work for <PERSON>. Though, a secret from her father's past is about to lead <PERSON> to her greatest challenge.", "description": "Exploring lost empires and finding priceless treasures... it's all in a day's work for <PERSON>. Though, a secret from her father's past is about to lead <PERSON> to her greatest challenge.", "duration": 8100000, "originalContentDuration": 6045000, "allotment": 8100, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5a9dd73dfb6f2f17481aff11/screenshot16_9_1748542155777.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5a9dd73dfb6f2f17481aff11/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5a9dd73dfb6f2f17481aff11/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5a9dd73dfb6f2f17481aff11/poster_1748542153261.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5a9dd73dfb6f2f17481aff11/screenshot4_3_1748542149027.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5a9dd73dfb6f2f17481aff11/screenshot16_9_1748542155777.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5a9dd73dfb6f2f17481aff11/poster16_9_1748542146361.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>[Executive Producer]", "<PERSON>"], "originalReleaseDate": "2001-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "602aba55f2c1bd001af23da8", "seriesID": "602aba54f2c1bd001af23da2", "slug": "lara-croft-tomb-raider-cradle-of-life-2003-1-1", "name": "<PERSON> Tomb Raider: The Cradle of Life", "summary": "<PERSON> sets out on a quest to save Pandora's box from landing into the wrong hands.  Join her as she races through furious hand-to-hand battles, blazing shoot-outs and breathtaking sky-diving escapes to try to save the ancient artifact.", "description": "<PERSON> sets out on a quest to save Pandora's box from landing in the wrong hands.  Join her as she races through furious hand-to-hand battles, blazing shoot-outs and breathtaking sky-diving escapes to try to save the ancient artifact.", "duration": 9000000, "originalContentDuration": 7050000, "allotment": 9000, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/602aba55f2c1bd001af23da8/screenshot16_9_1748392562290.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/602aba55f2c1bd001af23da8/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/602aba55f2c1bd001af23da8/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/602aba55f2c1bd001af23da8/poster_1748392561524.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/602aba55f2c1bd001af23da8/screenshot4_3_1748392561345.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/602aba55f2c1bd001af23da8/screenshot16_9_1748392562290.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/602aba55f2c1bd001af23da8/poster16_9_1748392561900.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2003-07-25T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "64d1409e334a8a0013966ab7", "seriesID": "64d1409a334a8a0013966a94", "slug": "american-hustle-2013-1-1", "name": "American Hustle", "summary": "The con is on when scam artists and lovers <PERSON> and <PERSON> are entrapped by ambitious FBI agent <PERSON> and coerced into participating in a sting operation that hinges on snaring politician <PERSON> and his associates.", "description": "The con is on when scam artists and lovers <PERSON> and <PERSON> are entrapped by ambitious FBI agent <PERSON> and coerced into participating in a sting operation that hinges on snaring politician <PERSON> and his associates.", "duration": ********, "originalContentDuration": 8295000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/64d1409e334a8a0013966ab7/screenshot16_9_1747412283389.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64d1409e334a8a0013966ab7/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64d1409e334a8a0013966ab7/poster_1747412286575.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64d1409e334a8a0013966ab7/screenshot4_3_1747412285741.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64d1409e334a8a0013966ab7/screenshot16_9_1747412283389.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/64d1409e334a8a0013966ab7/poster16_9_1747412283982.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Colleen <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2013-12-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6679969ac3f49b00131856e1", "seriesID": "66799698c3f49b00131856bc", "slug": "boyz-n-the-hood-1991-1-1", "name": "Boyz n the Hood", "summary": "Three friends struggle to survive in South Central Los Angeles where friendship, pain, danger, and love form a true picture of life in the 'hood in this critically acclaimed, action-filled story.", "description": "Three friends struggle to survive in South Central Los Angeles where friendship, pain, danger, and love form a true picture of life in the 'hood in this critically acclaimed, action-filled story.", "duration": 9000000, "originalContentDuration": 6735000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6679969ac3f49b00131856e1/screenshot16_9_1748020364913.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6679969ac3f49b00131856e1/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6679969ac3f49b00131856e1/poster_1748020364896.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6679969ac3f49b00131856e1/screenshot4_3_1748020364678.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6679969ac3f49b00131856e1/screenshot16_9_1748020364913.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6679969ac3f49b00131856e1/poster16_9_1748020364267.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Cuba Gooding", "Ice Cube", "<PERSON>", "Morris Chestnut"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1991-07-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "644971e71a5081001a13e9f7", "seriesID": "644971e41a5081001a13e9d4", "slug": "indiana-jones-and-the-raiders-of-the-lost-ark-1981-1-1", "name": "<PERSON> Jones and the Raiders of the Lost Ark", "summary": "Get ready for thrills in Indiana Jones and the Raiders of the Lost Ark. <PERSON> and his ex-flame <PERSON> dodge booby-traps, fight Nazis, and stare down snakes in their incredible worldwide quest for the mystical Ark of the Covenant.", "description": "Get ready for thrills in Indiana Jones and the Raiders of the Lost Ark. <PERSON> and his ex-flame <PERSON> dodge booby-traps, fight Nazis, and stare down snakes in their incredible worldwide quest for the mystical Ark of the Covenant.", "duration": 9000000, "originalContentDuration": 6930000, "allotment": 9000, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/644971e71a5081001a13e9f7/screenshot16_9_1744928648994.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/644971e71a5081001a13e9f7/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/644971e71a5081001a13e9f7/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/644971e71a5081001a13e9f7/poster_1744928647618.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/644971e71a5081001a13e9f7/screenshot4_3_1744928650280.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/644971e71a5081001a13e9f7/screenshot16_9_1744928648994.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/644971e71a5081001a13e9f7/poster16_9_1744928644069.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Elliott", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1981-06-12T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6449720a1a5081001a13edf3", "seriesID": "644972071a5081001a13edd0", "slug": "indiana-jones-and-the-temple-of-doom-1984-1-1", "name": "Indiana Jones and the Temple of Doom", "summary": "<PERSON> Jones and the Temple of Doom brings you thrills and excitement. <PERSON>, his sidekick <PERSON> and nightclub singer <PERSON> go from high-flying action above the Himalayas to a spine-tingling escape from a fortress-like mine in India.", "description": "<PERSON> Jones and the Temple of Doom brings you thrills and excitement. <PERSON>, his sidekick <PERSON> and nightclub singer <PERSON> go from high-flying action above the Himalayas to a spine-tingling escape from a fortress-like mine in India.", "duration": 9000000, "originalContentDuration": 7110000, "allotment": 9000, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/6449720a1a5081001a13edf3/screenshot16_9_1751392483072.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6449720a1a5081001a13edf3/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6449720a1a5081001a13edf3/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6449720a1a5081001a13edf3/poster_1751392483619.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6449720a1a5081001a13edf3/screenshot4_3_1751392482915.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6449720a1a5081001a13edf3/screenshot16_9_1751392483072.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6449720a1a5081001a13edf3/poster16_9_1751392482887.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1984-05-23T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "644986fa559c07001a11a337", "seriesID": "644986f8559c07001a11a315", "slug": "indiana-jones-and-the-last-crusade-1989-1-1", "name": "<PERSON> Jones and the Last Crusade", "summary": "There's nothing more exciting than trying to keep up with the Joneses in Indiana Jones and the Last Crusade. <PERSON>'s Nazi enemies are back and have kidnapped his father, in their effort to locate the sacred Holy Grail.", "description": "There's nothing more exciting than trying to keep up with the Joneses in Indiana Jones and the Last Crusade. <PERSON>'s Nazi enemies are back and have kidnapped his father, in their effort to locate the sacred Holy Grail.", "duration": 9900000, "originalContentDuration": 7620000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://mam-assets.clusters.pluto.tv/assets/manual/13dd4b3d-2ec9-4661-900b-abd7ba7d499f.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/644986fa559c07001a11a337/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/644986fa559c07001a11a337/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/644986fa559c07001a11a337/poster_1751438054459.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/644986fa559c07001a11a337/screenshot4_3_1751438054758.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://mam-assets.clusters.pluto.tv/assets/manual/13dd4b3d-2ec9-4661-900b-abd7ba7d499f.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/644986fa559c07001a11a337/poster16_9_1751438054618.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Elliott", "River Phoenix", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1989-05-24T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "64498c9f737654001d497d3f", "seriesID": "64498c9c737654001d497d1c", "slug": "indiana-jones-and-the-kingdom-of-the-crystal-skull-2008-1-1", "name": "Indiana Jones and the Kingdom of the Crystal Skull", "summary": "STEVEN SPIELBERG and GEORGE LUCAS bring you the greatest adventurer of all time! <PERSON> Jones and the Kingdom of the Crystal Skull finds <PERSON> trying to outrace a brilliant and beautiful agent for the mystical, all-powerful crystal skull of <PERSON><PERSON><PERSON>.", "description": "STEVEN SPIELBERG and GEORGE LUCAS bring you the greatest adventurer of all time! <PERSON> Jones and the Kingdom of the Crystal Skull finds <PERSON> trying to outrace a brilliant and beautiful agent for the mystical, all-powerful crystal skull of <PERSON><PERSON><PERSON>.", "duration": 9900000, "originalContentDuration": 7350000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/64498c9f737654001d497d3f/screenshot16_9_1748389682026.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64498c9f737654001d497d3f/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/64498c9f737654001d497d3f/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64498c9f737654001d497d3f/poster_1748389684992.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64498c9f737654001d497d3f/screenshot4_3_1748389683193.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64498c9f737654001d497d3f/screenshot16_9_1748389682026.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/64498c9f737654001d497d3f/poster16_9_1748389682242.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2008-05-22T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "6495eff09263a40013cf63a5", "seriesID": "6495efee9263a40013cf6382", "slug": "jack-reacher-2012-1-1", "name": "<PERSON>", "summary": "Investigator <PERSON> leaps off the pages of <PERSON>'s novel and onto the big screen in this thriller. When a horrible crime is committed, all evidence points to the suspect in custody, who offers up a single note in defense: \"Get <PERSON>!\"", "description": "Investigator <PERSON> leaps off the pages of <PERSON>'s novel and onto the big screen in this thriller. When a horrible crime is committed, all evidence points to the suspect in custody, who offers up a single note in defense: \"Get <PERSON>!\"", "duration": 9900000, "originalContentDuration": 7830000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/6495eff09263a40013cf63a5/screenshot16_9_1748042642507.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6495eff09263a40013cf63a5/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6495eff09263a40013cf63a5/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6495eff09263a40013cf63a5/poster_1748042642718.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6495eff09263a40013cf63a5/screenshot4_3_1748042642910.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6495eff09263a40013cf63a5/screenshot16_9_1748042642507.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6495eff09263a40013cf63a5/poster16_9_1748042642594.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2012-12-21T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "5bd3337867f34cef7af44774", "seriesID": "5bd3337767f34cef7af4476b", "slug": "patriot-games-1-1", "name": "Patriot Games", "summary": "<PERSON> is caught in the middle of a terrorist attack. <PERSON> helps to thwart <PERSON>’ assailants and becomes a local hero. But <PERSON>’s act marks him as a target. Now he must return to action to save his family.", "description": "<PERSON> is caught in the middle of a terrorist attack. <PERSON> helps to thwart <PERSON>’ assailants and becomes a local hero. But <PERSON>’s act marks him as a target. Now he must return to action to save his family.", "duration": 9000000, "originalContentDuration": 7005000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5bd3337867f34cef7af44774/screenshot16_9_1747849684000.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5bd3337867f34cef7af44774/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5bd3337867f34cef7af44774/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5bd3337867f34cef7af44774/poster_1747849683924.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5bd3337867f34cef7af44774/screenshot4_3_1747849685505.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5bd3337867f34cef7af44774/screenshot16_9_1747849684000.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5bd3337867f34cef7af44774/poster16_9_1747849683002.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1991-12-31T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66a82d023653a6001335510b", "seriesID": "66a82d003653a600133550e8", "slug": "star-trek-the-motion-picture-the-directors-cut-1979-1-1", "name": "Star Trek: The Motion Picture - The Director's Edition", "summary": "The U.S.S. Enterprise proudly soars again in this new, beautifully restored Director's Edition of the original Star Trek movie classic. This edition features enhanced visual effects and a new sound mix, supervised by legendary director <PERSON>.", "description": "The U.S.S. Enterprise proudly soars again in this new, beautifully restored Director's Edition of the original Star Trek movie classic. This edition features enhanced visual effects and a new sound mix, supervised by legendary director <PERSON>.", "duration": ********, "originalContentDuration": 8205000, "allotment": 10800, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/66a82d023653a6001335510b/screenshot16_9_1744932242093.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/66a82d023653a6001335510b/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/66a82d023653a6001335510b/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/66a82d023653a6001335510b/poster_1744932242799.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/66a82d023653a6001335510b/screenshot4_3_1744932240662.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/66a82d023653a6001335510b/screenshot16_9_1744932242093.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/66a82d023653a6001335510b/poster16_9_1744932241379.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1979-12-07T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "ad": true, "cc": true}, {"_id": "61ba3f6f601198001329f894", "seriesID": "61ba3f68601198001329f88d", "slug": "terminator-genisys-2015-1-1", "name": "Terminator Genisys", "summary": "When <PERSON> sends <PERSON> back to 1984 to protect a young <PERSON>, unexpected events alter the past and threaten the future for all mankind.  Charged with a new mission, <PERSON> must join forces with <PERSON> and her \"Guardian\" to save the world.", "description": "When <PERSON> sends <PERSON> back to 1984 to protect a young <PERSON>, unexpected events alter the past and threaten the future for all mankind.  Charged with a new mission, <PERSON> must join forces with <PERSON> and her \"Guardian\" to save the world.", "duration": 9900000, "originalContentDuration": 7545000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/61ba3f6f601198001329f894/screenshot16_9_1748471438116.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/61ba3f6f601198001329f894/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/61ba3f6f601198001329f894/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/61ba3f6f601198001329f894/poster_1748471439820.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/61ba3f6f601198001329f894/screenshot4_3_1748471531347.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/61ba3f6f601198001329f894/screenshot16_9_1748471438116.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/61ba3f6f601198001329f894/poster16_9_1748471476510.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2015-07-01T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "5f208fa328a096001af9d587", "seriesID": "5f208df671e4fd001a31a8e3", "slug": "get-rich-or-die-tryin-2005-1-1", "name": "Get Rich or Die Tryin'", "summary": "Following a near-fatal gun battle, <PERSON> recalls his journey from orphaned street kid from the Bronx to making his mark in the drug trade to teaming up with a fellow ex-con (<PERSON><PERSON>) for his shot at becoming a successful rap artist.", "description": "Following a near-fatal gun battle, <PERSON> recalls his journey from orphaned street kid from the Bronx to making his mark in the drug trade to teaming up with a fellow ex-con (<PERSON><PERSON>) for his shot at becoming a successful rap artist.", "duration": 9000000, "originalContentDuration": 6990000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5f208fa328a096001af9d587/screenshot16_9_1748022841805.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Musical", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5f208fa328a096001af9d587/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5f208fa328a096001af9d587/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5f208fa328a096001af9d587/poster_1748022841509.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5f208fa328a096001af9d587/screenshot4_3_1748022841981.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5f208fa328a096001af9d587/screenshot16_9_1748022841805.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5f208fa328a096001af9d587/poster16_9_1748022841658.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Adewale Akinnouye-Agbaje", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2005-11-09T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5fdb89d3aabcdd001aaf82f0", "seriesID": "5fdb89cfaabcdd001aaf82e9", "slug": "the-art-of-war-2000-1-1", "name": "The Art of War", "summary": "UN's secretary general uses covert operations to help diplomacy along. <PERSON>'s called back 6 months after one such operation. He witnesses the murder of Chinese UN ambassador at UN, NYC, chases the assassin and ends up a suspect.", "description": "UN's secretary-general uses covert operations to help diplomacy along. <PERSON>'s called back 6 months after one such operation. He witnesses the murder of a Chinese UN ambassador at UN, NYC, chases the assassin and ends up a suspect.", "duration": 9000000, "originalContentDuration": 7020000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5fdb89d3aabcdd001aaf82f0/screenshot16_9_1751490722055.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5fdb89d3aabcdd001aaf82f0/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5fdb89d3aabcdd001aaf82f0/poster_1751490722593.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5fdb89d3aabcdd001aaf82f0/screenshot4_3_1751490722688.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5fdb89d3aabcdd001aaf82f0/screenshot16_9_1751490722055.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5fdb89d3aabcdd001aaf82f0/poster16_9_1751490721770.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-08-23T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6238d50c793624001313c3fd", "seriesID": "6238d508793624001313c3f5", "slug": "drive-2011-1-1", "name": "Drive", "summary": "A Hollywood stunt performer (<PERSON>) who moonlights as a wheelman discovers that a contract has been put on him after a heist gone wrong.", "description": "A Hollywood stunt performer (<PERSON>) who moonlights as a wheelman discovers that a contract has been put on him after a heist gone wrong.", "duration": 8100000, "originalContentDuration": 6030000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6238d50c793624001313c3fd/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6238d50c793624001313c3fd/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6238d50c793624001313c3fd/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6238d50c793624001313c3fd/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6238d50c793624001313c3fd/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6238d50c793624001313c3fd/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2011-09-15T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5b36cbc0ad1b6cc6521599ac", "seriesID": "5b36cbc0ad1b6cc6521599a8", "slug": "hollow-man-2000-1-1", "name": "Hollow Man", "summary": "<PERSON> and <PERSON> play former lovers who, along with a team of other scientists like themselves, discover the secret of invisibility. Rating: R", "description": "<PERSON> and <PERSON> play former lovers who, along with a team of other scientists like themselves, discover the secret of invisibility. Rating: R", "duration": 9000000, "originalContentDuration": 6765000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5b36cbc0ad1b6cc6521599ac/screenshot16_9_1748620082083.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5b36cbc0ad1b6cc6521599ac/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5b36cbc0ad1b6cc6521599ac/poster_1748620082217.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5b36cbc0ad1b6cc6521599ac/screenshot4_3_1748620082344.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5b36cbc0ad1b6cc6521599ac/screenshot16_9_1748620082083.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5b36cbc0ad1b6cc6521599ac/poster16_9_1748620082287.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-08-04T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "66a7dfaa5b669a00139e3f53", "seriesID": "66a7dfa85b669a00139e3f2d", "slug": "julie-and-julia-2009-1-1", "name": "Julie & Julia", "summary": "<PERSON> <PERSON> Julia, tells the true stories of how <PERSON>'s life and cookbook inspired fledgling writer <PERSON> to whip up 524 recipes in 365 days and introduce a new generation to the magic of French cooking.", "description": "<PERSON> <PERSON> Julia, tells the true stories of how <PERSON>'s life and cookbook inspired fledgling writer <PERSON> to whip up 524 recipes in 365 days and introduce a new generation to the magic of French cooking.", "duration": 9900000, "originalContentDuration": 7410000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/66a7dfaa5b669a00139e3f53/screenshot16_9_1748352963551.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/66a7dfaa5b669a00139e3f53/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/66a7dfaa5b669a00139e3f53/poster_1748352962914.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/66a7dfaa5b669a00139e3f53/screenshot4_3_1748352962221.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/66a7dfaa5b669a00139e3f53/screenshot16_9_1748352963551.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/66a7dfaa5b669a00139e3f53/poster16_9_1748352962533.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2009-08-05T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "5b36cbb4ad1b6cc6521597d2", "seriesID": "5b36cbb4ad1b6cc6521597ce", "slug": "last-action-hero-1993-1-1", "name": "Last Action Hero", "summary": "Action-adventure superstar <PERSON> bursts through the screen as a larger-than-life movie hero in this nonstop adventure from acclaimed director <PERSON> (Predator, Die Hard). Rating: PG-13", "description": "Action-adventure superstar <PERSON> bursts through the screen as a larger-than-life movie hero in this nonstop adventure from acclaimed director <PERSON> (Predator, Die Hard). Rating: PG-13", "duration": 9900000, "originalContentDuration": 7860000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5b36cbb4ad1b6cc6521597d2/screenshot16_9_1744930447615.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5b36cbb4ad1b6cc6521597d2/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5b36cbb4ad1b6cc6521597d2/poster_1744930457187.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5b36cbb4ad1b6cc6521597d2/screenshot4_3_1744930457217.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5b36cbb4ad1b6cc6521597d2/screenshot16_9_1744930447615.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5b36cbb4ad1b6cc6521597d2/poster16_9_1744930450162.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> Ruehl", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1993-06-18T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "601311f867e9ba001a83e3a9", "seriesID": "601311f667e9ba001a83e3a2", "slug": "pompeii-2014-1-1", "name": "Pompeii", "summary": "Set in 79 A.D., POMPEII tells the epic story of <PERSON>, a slave turned invincible gladiator who finds himself in a race against time to save his true love, <PERSON><PERSON>, the beautiful daughter of a wealthy merchant.", "description": "Set in 79 A.D., POMPEII tells the epic story of <PERSON>, a slave turned invincible gladiator who finds himself in a race against time to save his true love, <PERSON><PERSON>, the beautiful daughter of a wealthy merchant.", "duration": 8100000, "originalContentDuration": 6315000, "allotment": 8100, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/601311f867e9ba001a83e3a9/screenshot16_9_1749143920977.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/601311f867e9ba001a83e3a9/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/601311f867e9ba001a83e3a9/poster_1749143965695.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/601311f867e9ba001a83e3a9/screenshot4_3_1749143936189.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/601311f867e9ba001a83e3a9/screenshot16_9_1749143920977.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/601311f867e9ba001a83e3a9/poster16_9_1749143910116.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "Carrie-<PERSON>", "<PERSON>", "Adewale Akinnuoye-Agbaje", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2014-02-19T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "60358654b2b870001a0409f6", "seriesID": "60358652b2b870001a0409ef", "slug": "snatch-2000-1-1", "name": "Snatch", "summary": "A diamond heist gone haywire launches gangsters, bookies, and a dog on a rollicking ride through the rugged world of bare-knuckle boxing in search of the missing stone. Critically acclaimed comedy from <PERSON>.", "description": "A diamond heist went haywire launches gangsters, bookies, and a dog on a rollicking ride through the rugged world of bare-knuckle boxing in search of the missing stone. Critically acclaimed comedy from <PERSON>.", "duration": 8100000, "originalContentDuration": 6165000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/60358654b2b870001a0409f6/screenshot16_9_1748622602014.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/60358654b2b870001a0409f6/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/60358654b2b870001a0409f6/poster_1748622603218.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/60358654b2b870001a0409f6/screenshot4_3_1748622603339.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/60358654b2b870001a0409f6/screenshot16_9_1748622602014.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/60358654b2b870001a0409f6/poster16_9_1748622603328.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>o <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "2000-08-31T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5e337fe66cbe9e99c44bcb54", "seriesID": "5e337fe46cbe9e99c44bcb4d", "slug": "stealth-2005-1-1", "name": "Stealth", "summary": "From the director of XXX and The Fast and The Furious comes an exhilarating epic blockbuster starring <PERSON>, <PERSON> and Academy Award® Winner <PERSON> (Best Actor, RAY, 2004). rated -PG-13", "description": "From the director of XXX and The Fast and The Furious comes an exhilarating epic blockbuster starring <PERSON>, <PERSON> and Academy Award® Winner <PERSON> (Best Actor, RAY, 2004). rated -PG-13", "duration": 9000000, "originalContentDuration": 7260000, "allotment": 9000, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5e337fe66cbe9e99c44bcb54/screenshot16_9_1748496243302.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5e337fe66cbe9e99c44bcb54/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5e337fe66cbe9e99c44bcb54/poster_1748496243111.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5e337fe66cbe9e99c44bcb54/screenshot4_3_1748496242356.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5e337fe66cbe9e99c44bcb54/screenshot16_9_1748496243302.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5e337fe66cbe9e99c44bcb54/poster16_9_1748496242101.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON><PERSON><PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2005-07-27T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "621027c322dc3f0013e19740", "seriesID": "621027c022dc3f0013e19738", "slug": "tears-of-the-sun-2003-1-1", "name": "Tears of the Sun", "summary": "After a West African president's family is assassinated and civil unrest is rampant, a top-secret team is deployed to the far reaches of the jungle to rescue a U.S. doctor who is acting as a missionary.", "description": "After a West African president's family is assassinated and civil unrest is rampant, a top-secret team is deployed to the far reaches of the jungle to rescue a U.S. doctor who is acting as a missionary.", "duration": 9900000, "originalContentDuration": 7260000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/621027c322dc3f0013e19740/screenshot16_9_1747851120937.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/621027c322dc3f0013e19740/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/621027c322dc3f0013e19740/poster_1747851122296.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/621027c322dc3f0013e19740/screenshot4_3_1747851122500.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/621027c322dc3f0013e19740/screenshot16_9_1747851120937.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/621027c322dc3f0013e19740/poster16_9_1747851120870.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2003-03-06T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "67ffd6d5a8cdcdbb7d8b4d16", "seriesID": "67ffd6d3a8cdcdbb7d8b4ceb", "slug": "the-girl-in-the-spiders-web-2018-1-1", "name": "The Girl in the Spider's Web", "summary": "Young computer hacker <PERSON><PERSON><PERSON> and journalist <PERSON><PERSON><PERSON> find themselves caught in a web of spies, cybercriminals, and corrupt government officials.", "description": "Young computer hacker <PERSON><PERSON><PERSON> and journalist <PERSON><PERSON><PERSON> find themselves caught in a web of spies, cybercriminals, and corrupt government officials.", "duration": 9000000, "originalContentDuration": 6930000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/67ffd6d5a8cdcdbb7d8b4d16/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/67ffd6d5a8cdcdbb7d8b4d16/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/67ffd6d5a8cdcdbb7d8b4d16/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/67ffd6d5a8cdcdbb7d8b4d16/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/67ffd6d5a8cdcdbb7d8b4d16/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/67ffd6d5a8cdcdbb7d8b4d16/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Lakeith <PERSON>field", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON><PERSON>"], "writers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "2018-10-18T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "5a9da842fb6f2f17481afa5f", "seriesID": "5a9da841fb6f2f17481afa5a", "slug": "clue-1985-1-1", "name": "Clue", "summary": "Meet all the notorious suspects and discover all their foul play things. You'll love their dastardly doings as the bodies and the laughs pile up before your eyes. Featuring all three surprise endings!!", "description": "Meet all the notorious suspects and discover all their foul play things. You'll love their dastardly doings as the bodies and the laughs pile up before your eyes. Featuring all three surprise endings!!", "duration": 7200000, "originalContentDuration": 5820000, "allotment": 7200, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/5a9da842fb6f2f17481afa5f/screenshot16_9_1747187641671.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5a9da842fb6f2f17481afa5f/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5a9da842fb6f2f17481afa5f/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5a9da842fb6f2f17481afa5f/poster_1747187642650.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5a9da842fb6f2f17481afa5f/screenshot4_3_1747187642315.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5a9da842fb6f2f17481afa5f/screenshot16_9_1747187641671.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5a9da842fb6f2f17481afa5f/poster16_9_1747187641534.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "Colleen <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1985-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6579f8acb2cbaf00137677b5", "seriesID": "6579f8acb2cbaf00137677a8", "slug": "fatal-attraction-ca-1987-1-1-ptv1", "name": "Fatal Attraction", "summary": "<PERSON> plays <PERSON>, a New York attorney who has a tryst with seductive <PERSON> while his wife is away. <PERSON> later shrugs off the affair as a mistake and considers it over. But <PERSON> won't be ignored. Not now, not tomorrow, not ever.", "description": "<PERSON> plays <PERSON>, a New York attorney who has a tryst with seductive <PERSON> while his wife is away. <PERSON> later shrugs off the affair as a mistake and considers it over. But <PERSON> won't be ignored. Not now, not tomorrow, not ever.", "duration": 9000000, "originalContentDuration": 7170000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6579f8acb2cbaf00137677b5/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6579f8acb2cbaf00137677b5/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6579f8acb2cbaf00137677b5/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6579f8acb2cbaf00137677b5/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6579f8acb2cbaf00137677b5/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6579f8acb2cbaf00137677b5/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6579f8acb2cbaf00137677b5/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "1987-09-18T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6254a4ea3f03ed0013c40f3c", "seriesID": "6254a4e73f03ed0013c40f34", "slug": "footloose-1984-1984-1-1", "name": "<PERSON><PERSON><PERSON>", "summary": "A rebellious city boy finds himself in an uptight Midwestern town where dancing and therefore all youthful joy has been banned.", "description": "A rebellious city boy finds himself in an uptight Midwestern town where dancing and therefore all youthful joy has been banned.", "duration": 8100000, "originalContentDuration": 6435000, "allotment": 8100, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/6254a4ea3f03ed0013c40f3c/screenshot16_9_1748021044195.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6254a4ea3f03ed0013c40f3c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6254a4ea3f03ed0013c40f3c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6254a4ea3f03ed0013c40f3c/poster_1748021044208.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6254a4ea3f03ed0013c40f3c/screenshot4_3_1748021042527.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6254a4ea3f03ed0013c40f3c/screenshot16_9_1748021044195.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6254a4ea3f03ed0013c40f3c/poster16_9_1748021042695.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Gene Pack", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1984-02-17T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5d9b9ab602119c67cb64e9fe", "seriesID": "5d9b9ab302119c67cb64e9f7", "slug": "good-burger-1996-1-1", "name": "Good Burger", "summary": "Ke<PERSON> are serving up laughs! They're the good guys in Good Burger, a zany comedy inspired by the popular <PERSON><PERSON> routine from Nickelodeon's hit series All That.", "description": "<PERSON><PERSON> & <PERSON><PERSON> are serving up laughs! They're the good guys in Good Burger, a zany comedy inspired by the popular <PERSON><PERSON> routine from Nickelodeon's hit series All That.", "duration": 7200000, "originalContentDuration": 5730000, "allotment": 7200, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/5d9b9ab602119c67cb64e9fe/screenshot16_9_1748042290393.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5d9b9ab602119c67cb64e9fe/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5d9b9ab602119c67cb64e9fe/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5d9b9ab602119c67cb64e9fe/poster_1748042290034.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5d9b9ab602119c67cb64e9fe/screenshot4_3_1748042289935.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5d9b9ab602119c67cb64e9fe/screenshot16_9_1748042290393.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5d9b9ab602119c67cb64e9fe/poster16_9_1748042290171.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Sinbad"], "directors": ["<PERSON>"], "originalReleaseDate": "1997-07-25T00:00:00Z"}, "avail": {}, "ad": true, "cc": true}, {"_id": "5ab2fb92b71f50edcf89a97e", "seriesID": "5ab2fb92b71f50edcf89a979", "slug": "hustle-and-flow-2005-1-1", "name": "Hustle & Flow", "summary": "The tale of <PERSON><PERSON>, a pimp whose hustle selling sexy <PERSON><PERSON> leaves him wondering if this is it for him. Only when he trades contraband for a keyboard and bumps into his old schoolfriend <PERSON> does he see a way out by laying down some tracks.", "description": "The tale of <PERSON><PERSON>, a pimp whose hustle selling sexy <PERSON><PERSON> leaves him wondering if this is it for him. Only when he trades contraband for a keyboard and bumps into his old school friend <PERSON> does he see a way out by laying down some tracks.", "duration": 9000000, "originalContentDuration": 6990000, "allotment": 9000, "rating": "Not Rated", "featuredImage": {"path": "https://images.pluto.tv/episodes/5ab2fb92b71f50edcf89a97e/screenshot16_9_1748539802632.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5ab2fb92b71f50edcf89a97e/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5ab2fb92b71f50edcf89a97e/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5ab2fb92b71f50edcf89a97e/poster_1748539802116.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5ab2fb92b71f50edcf89a97e/screenshot4_3_1748539802852.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5ab2fb92b71f50edcf89a97e/screenshot16_9_1748539802632.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5ab2fb92b71f50edcf89a97e/poster16_9_1748539801750.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2005-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "602abc2e41fd63001a5a76da", "seriesID": "602abc2c41fd63001a5a76d2", "slug": "the-truman-show-1998-1-1-ptv4", "name": "The Truman Show", "summary": "<PERSON> doesn’t realize that his quaint hometown is a giant studio set run by a visionary producer/director/creator (<PERSON>), that folks living and working there are Hollywood actors, that even his incessantly bubbly wife is a contract player.", "description": "<PERSON> doesn’t realize that his quaint hometown is a giant studio set run by a visionary producer/director/creator (<PERSON>), that folks living and working there are Hollywood actors, that even his incessantly bubbly wife is a contract player.", "duration": 8100000, "originalContentDuration": 6180000, "allotment": 8100, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/602abc2e41fd63001a5a76da/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/602abc2e41fd63001a5a76da/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/602abc2e41fd63001a5a76da/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/602abc2e41fd63001a5a76da/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/602abc2e41fd63001a5a76da/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/602abc2e41fd63001a5a76da/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/602abc2e41fd63001a5a76da/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON> Taylor", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1998-06-05T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5cc22384251c9fc6e72de15c", "seriesID": "5cc22381251c9fc6e72de155", "slug": "the-wood-1-1", "name": "The Wood", "summary": "Lots of guys have second thoughts about marriage. Three hours before his wedding, <PERSON> is having third, fourth and fifth thoughts. Good thing he’s got buddies <PERSON> and <PERSON> around to help.", "description": "Lots of guys have second thoughts about marriage. Three hours before his wedding, <PERSON> is having third, fourth and fifth thoughts. Good thing he’s got buddies <PERSON> and <PERSON> around to help.", "duration": 8100000, "originalContentDuration": 6405000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5cc22384251c9fc6e72de15c/screenshot16_9_1751497922142.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5cc22384251c9fc6e72de15c/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5cc22384251c9fc6e72de15c/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5cc22384251c9fc6e72de15c/poster_1751497923490.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5cc22384251c9fc6e72de15c/screenshot4_3_1751497922891.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5cc22384251c9fc6e72de15c/screenshot16_9_1751497922142.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5cc22384251c9fc6e72de15c/poster16_9_1751497923480.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1999-07-16T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "64dfa60db97a1e0013171e1e", "seriesID": "64dfa60db97a1e0013171e11", "slug": "coming-to-america-en-1988-1-1-ptv1", "name": "Coming to America", "summary": "Join <PERSON><PERSON><PERSON><PERSON> MURPHY on an unforgettable comic quest to the New World. As an African Prince, it's time for him to find a princess. The mission leads him and his most loyal friend (ARSENIO HALL) to New York. <PERSON> and <PERSON> in classic cameo roles!", "description": "Join <PERSON><PERSON><PERSON><PERSON> MURPHY on an unforgettable comic quest to the New World. As an African Prince, it's time for him to find a princess. The mission leads him and his most loyal friend (ARSENIO HALL) to New York. <PERSON> and <PERSON> in classic cameo roles!", "duration": 9000000, "originalContentDuration": 7020000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/64dfa60db97a1e0013171e1e/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64dfa60db97a1e0013171e1e/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/64dfa60db97a1e0013171e1e/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64dfa60db97a1e0013171e1e/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64dfa60db97a1e0013171e1e/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64dfa60db97a1e0013171e1e/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/64dfa60db97a1e0013171e1e/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "Arsenio Hall", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>.", "<PERSON>"], "originalReleaseDate": "1988-06-26T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "616e1088b8333a001ad7cc4a", "seriesID": "616e1088b8333a001ad7cc48", "slug": "tigerland-1999-1-1-ptv1", "name": "Tigerland", "summary": "As a nation stands divided over the war in Vietnam, stark differences of opinion surface among the men of A-Company, triggering unexpected and far-reaching consequences.", "description": "As a nation stands divided over the war in Vietnam, stark differences of opinion surface among the men of A-Company, triggering unexpected and far-reaching consequences.", "duration": 8100000, "originalContentDuration": 6060000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/616e1088b8333a001ad7cc4a/screenshot16_9_1749486962524.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/616e1088b8333a001ad7cc4a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/616e1088b8333a001ad7cc4a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/616e1088b8333a001ad7cc4a/poster_1749486963152.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/616e1088b8333a001ad7cc4a/screenshot4_3_1749486963180.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/616e1088b8333a001ad7cc4a/screenshot16_9_1749486962524.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/616e1088b8333a001ad7cc4a/poster16_9_1749486962872.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"originalReleaseDate": "2000-01-01T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5fbea5aa861be7001a6be7cb", "seriesID": "5fbea5a8861be7001a6be7c0", "slug": "cop-land-1997-1-1", "name": "Cop Land", "summary": "Director <PERSON> (FORD vs FERRARI) extends his crime thriller classic that brings together a powerhouse cast, including <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "description": "Director <PERSON> (FORD vs FERRARI) extends his crime thriller classic that brings together a powerhouse cast, including <PERSON>, <PERSON>, <PERSON>, and <PERSON>.", "duration": 8100000, "originalContentDuration": 6300000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5fbea5aa861be7001a6be7cb/screenshot16_9_1751437451028.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5fbea5aa861be7001a6be7cb/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5fbea5aa861be7001a6be7cb/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5fbea5aa861be7001a6be7cb/poster_1751437450418.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://mam-assets.clusters.pluto.tv/assets/c2eb6df7-b713-4b1a-adbc-ef1c30734a89.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5fbea5aa861be7001a6be7cb/screenshot16_9_1751437451028.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5fbea5aa861be7001a6be7cb/poster16_9_1751437456834.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1997-08-15T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "654a4b563e6938001ade0cca", "seriesID": "654a4b533e6938001ade0ca7", "slug": "first-blood-1982-1-1", "name": "First Blood", "summary": "A soft-spoken Vietnam vet drifts into a small town looking for no trouble, but finds it in the form of a psychotic local sheriff who finds pleasure in hating him for no reason.", "description": "A soft-spoken Vietnam vet drifts into a small town looking for no trouble, but finds it in the form of a psychotic local sheriff who finds pleasure in hating him for no reason.", "duration": 7200000, "originalContentDuration": 5595000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/654a4b563e6938001ade0cca/screenshot16_9_1749476882585.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/654a4b563e6938001ade0cca/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/654a4b563e6938001ade0cca/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/654a4b563e6938001ade0cca/poster_1749476883900.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/654a4b563e6938001ade0cca/screenshot4_3_1749476883201.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/654a4b563e6938001ade0cca/screenshot16_9_1749476882585.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/654a4b563e6938001ade0cca/poster16_9_1749476882654.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1982-10-22T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "6137ee7dc79f030013e056d0", "seriesID": "6137ee7bc79f030013e056c9", "slug": "the-godfather-1972-1-1", "name": "The Godfather", "summary": "A chilling portrait of the <PERSON><PERSON><PERSON> family's rise and near fall from power in America along with balancing the story of the Sicilian clan's ugly crime business in which they are engaged.", "description": "A chilling portrait of the <PERSON><PERSON><PERSON> family's rise and near fall from power in America along with balancing the story of the Sicilian clan's ugly crime business in which they are engaged.", "duration": 13500000, "originalContentDuration": 10635000, "allotment": 13500, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6137ee7dc79f030013e056d0/screenshot16_9_1749148562257.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6137ee7dc79f030013e056d0/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6137ee7dc79f030013e056d0/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6137ee7dc79f030013e056d0/poster_1749148562438.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6137ee7dc79f030013e056d0/screenshot4_3_1749148562103.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6137ee7dc79f030013e056d0/screenshot16_9_1749148562257.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6137ee7dc79f030013e056d0/poster16_9_1749148562046.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON>", "Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Talia Shire", "Al <PERSON>i"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1972-03-15T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "6137eed962aa0500144d867d", "seriesID": "6137eed762aa0500144d8676", "slug": "the-godfather-part-ii-1974-1-1", "name": "The Godfather Part II", "summary": "The continuing saga of a young <PERSON><PERSON> growing up in Sicily and in New York during the 1910s and then following <PERSON> in the 1950s as he tries to expand the family business.", "description": "The continuing saga of a young <PERSON><PERSON> growing up in Sicily and in New York during the 1910s and then following <PERSON> in the 1950s as he tries to expand the family business.", "duration": 14400000, "originalContentDuration": 12135000, "allotment": 14400, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6137eed962aa0500144d867d/screenshot16_9_1749151818481.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6137eed962aa0500144d867d/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6137eed962aa0500144d867d/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6137eed962aa0500144d867d/poster_1749151840921.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6137eed962aa0500144d867d/screenshot4_3_1749151818410.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6137eed962aa0500144d867d/screenshot16_9_1749151818481.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6137eed962aa0500144d867d/poster16_9_1749151817719.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Talia Shire", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1974-12-12T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5d24d164f55a8b137789813d", "seriesID": "5d24d162f55a8b1377898137", "slug": "hamburger-hill-1-1", "name": "Hamburger Hill", "summary": "On the 11th May 1969, troops of the 101st airborne division engaged the enemy at the base of Hill 937 in the Ashau Valley. Ten days and eleven bloody assaults later, the troops who fought there called it Hamburger Hill.", "description": "On the 11th May 1969, troops of the 101st airborne division engaged the enemy at the base of Hill 937 in the Ashau Valley. Ten days and eleven bloody assaults later, the troops who fought there called it Hamburger Hill.", "duration": 9000000, "originalContentDuration": 6600000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5d24d164f55a8b137789813d/screenshot16_9_1751469482287.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "War", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5d24d164f55a8b137789813d/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5d24d164f55a8b137789813d/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5d24d164f55a8b137789813d/poster_1751469482256.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5d24d164f55a8b137789813d/screenshot4_3_1751469481429.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5d24d164f55a8b137789813d/screenshot16_9_1751469482287.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5d24d164f55a8b137789813d/poster16_9_1751469482535.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1987-01-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "613fccc2766f2a00142e6a54", "seriesID": "613fccc0766f2a00142e6a4d", "slug": "no-country-for-old-men-miramax-2007-1-1", "name": "No Country For Old Men", "summary": "Winner of four Academy Awards, including Best Picture, filmmakers <PERSON> and <PERSON> (FARGO) bring to the screen a thrilling bestseller in which a crime-scene's irresistible stolen loot sets forth an unstoppable chain reaction of violence.", "description": "Winner of four Academy Awards, including Best Picture, filmmakers <PERSON> and <PERSON> (FARGO) bring to the screen a thrilling bestseller in which a crime-scene's irresistible stolen loot sets forth an unstoppable chain reaction of violence.", "duration": 9900000, "originalContentDuration": 7335000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/613fccc2766f2a00142e6a54/screenshot16_9_1751472002003.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/613fccc2766f2a00142e6a54/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/613fccc2766f2a00142e6a54/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/613fccc2766f2a00142e6a54/poster_1751472003006.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/613fccc2766f2a00142e6a54/screenshot4_3_1751472002033.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/613fccc2766f2a00142e6a54/screenshot16_9_1751472002003.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/613fccc2766f2a00142e6a54/poster16_9_1751472001917.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2007-11-09T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "654a4885939c4b001a54d842", "seriesID": "654a4882939c4b001a54d81e", "slug": "rambo-first-blood-part-ii-1985-1-1", "name": "Rambo: First Blood Part II", "summary": "<PERSON> is sent on assignment back to Southeast Asia to uncover evidence of still missing POWs, and ends up with his hands full when he comes up against Vietnamese soldiers still guarding the camps.", "description": "<PERSON> is sent on assignment back to Southeast Asia to uncover evidence of still missing POWs, and ends up with his hands full when he comes up against Vietnamese soldiers still guarding the camps.", "duration": 7200000, "originalContentDuration": 5760000, "allotment": 7200, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/654a4885939c4b001a54d842/screenshot16_9_1749480482599.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/654a4885939c4b001a54d842/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/654a4885939c4b001a54d842/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/654a4885939c4b001a54d842/poster_1749480482476.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/654a4885939c4b001a54d842/screenshot4_3_1749480483247.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/654a4885939c4b001a54d842/screenshot16_9_1749480482599.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/654a4885939c4b001a54d842/poster16_9_1749480482568.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>", "<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1985-05-22T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "654aa558e2a2d8001a50139a", "seriesID": "654aa557e2a2d8001a501377", "slug": "rambo-iii-1988-1-1", "name": "Rambo III", "summary": "<PERSON><PERSON> refuses to join his friend and former mentor, Colonel <PERSON><PERSON><PERSON><PERSON>, on a mission to Afghanistan. The mission fails and <PERSON><PERSON><PERSON><PERSON> is captured by Russian troops. Now <PERSON><PERSON> must infiltrate the Russian fortress to save his only friend.", "description": "<PERSON><PERSON> refuses to join his friend and former mentor, Colonel <PERSON><PERSON><PERSON><PERSON>, on a mission to Afghanistan. The mission fails and <PERSON><PERSON><PERSON><PERSON> is captured by Russian troops. Now <PERSON><PERSON> must infiltrate the Russian fortress to save his only friend.", "duration": 8100000, "originalContentDuration": 6105000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/654aa558e2a2d8001a50139a/screenshot16_9_1749515451419.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/654aa558e2a2d8001a50139a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/654aa558e2a2d8001a50139a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/654aa558e2a2d8001a50139a/poster_1749480122507.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/654aa558e2a2d8001a50139a/screenshot4_3_1749480122414.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/654aa558e2a2d8001a50139a/screenshot16_9_1749515451419.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/654aa558e2a2d8001a50139a/poster16_9_1749480121988.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON><PERSON><PERSON>", "<PERSON>sson G<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Alon <PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1988-05-25T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5e6d175b89acba001abc3556", "seriesID": "5e6d175a89acba001abc354c", "slug": "road-to-perdition-2001-1-1", "name": "Road to Perdition", "summary": "Bonds of loyalty are put to the test when a hitman's son witnesses what his father does for a living.", "description": "Bonds of loyalty are put to the test when a hitman's son witnesses what his father does for a living.", "duration": 9000000, "originalContentDuration": 7035000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5e6d175b89acba001abc3556/screenshot16_9_1749483724690.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5e6d175b89acba001abc3556/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5e6d175b89acba001abc3556/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5e6d175b89acba001abc3556/poster_1749483723620.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5e6d175b89acba001abc3556/screenshot4_3_1749483725056.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5e6d175b89acba001abc3556/screenshot16_9_1749483724690.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5e6d175b89acba001abc3556/poster16_9_1749483724431.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "2002-01-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5cc35d2835f85ad1b1a12b5f", "seriesID": "5cc22373251c9fc6e72de0f3", "slug": "the-running-man-1-1", "name": "The Running Man", "summary": "A wrongly-convicted man must try to survive a public execution gauntlet staged as a TV game show.", "description": "A wrongly-convicted man must try to survive a public execution gauntlet staged as a TV game show.", "duration": 8100000, "originalContentDuration": 6060000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5cc35d2835f85ad1b1a12b5f/screenshot16_9_1747863720786.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5cc35d2835f85ad1b1a12b5f/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5cc35d2835f85ad1b1a12b5f/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5cc35d2835f85ad1b1a12b5f/poster_1747939112664.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5cc35d2835f85ad1b1a12b5f/screenshot4_3_1747863720853.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5cc35d2835f85ad1b1a12b5f/screenshot16_9_1747863720786.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5cc35d2835f85ad1b1a12b5f/poster16_9_1747939112817.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Prof. <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1987-01-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "60369bf405d744001a21424b", "seriesID": "60369bf205d744001a214244", "slug": "saving-private-ryan-paramount-pictures-lf-1998-1-1", "name": "Saving Private <PERSON>", "summary": "A World War II captain (<PERSON>) and his squad (<PERSON>, <PERSON>) risk all to locate and send home a soldier whose three brothers died in combat.", "description": "A World War II captain (<PERSON>) and his squad (<PERSON>, <PERSON>) risk all to locate and send home a soldier whose three brothers died in combat.", "duration": 13500000, "originalContentDuration": 10170000, "allotment": 13500, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/60369bf405d744001a21424b/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/60369bf405d744001a21424b/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/60369bf405d744001a21424b/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/60369bf405d744001a21424b/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/60369bf405d744001a21424b/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/60369bf405d744001a21424b/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/60369bf405d744001a21424b/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Vin Diesel"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1998-07-21T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "ad": true, "cc": true}, {"_id": "5f50317b3bb7850013d31885", "seriesID": "5f5031793bb7850013d3187e", "slug": "the-sum-of-all-fears-2002-1-1", "name": "The Sum of All Fears", "summary": "East-West tensions erupt when the CIA suspects that renegade Russian scientists are developing more nuclear weapons.", "description": "East-West tensions erupt when the CIA suspects that renegade Russian scientists are developing more nuclear weapons.", "duration": 9900000, "originalContentDuration": 7425000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5f50317b3bb7850013d31885/screenshot16_9_1749223441966.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5f50317b3bb7850013d31885/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5f50317b3bb7850013d31885/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5f50317b3bb7850013d31885/poster_1749223441633.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5f50317b3bb7850013d31885/screenshot4_3_1749223441983.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5f50317b3bb7850013d31885/screenshot16_9_1749223441966.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5f50317b3bb7850013d31885/poster16_9_1749223441811.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "originalReleaseDate": "2002-05-31T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "6410e5f2803fcf0013284166", "seriesID": "6410e5f0803fcf0013284142", "slug": "top-gun-1986-1-1", "name": "Top Gun", "summary": "<PERSON> is superb as <PERSON><PERSON><PERSON>, a  young flyer who's out to become the best. And <PERSON> plays the instructor who teaches Maverick a few things you can't learn in a classroom.", "description": "<PERSON> is superb as <PERSON><PERSON><PERSON>, a  young flyer who's out to become the best. And <PERSON> plays the instructor who teaches Maverick a few things you can't learn in a classroom.", "duration": 9000000, "originalContentDuration": 6570000, "allotment": 9000, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/6410e5f2803fcf0013284166/screenshot16_9_1751497928055.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6410e5f2803fcf0013284166/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6410e5f2803fcf0013284166/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6410e5f2803fcf0013284166/poster_1751497926424.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6410e5f2803fcf0013284166/screenshot4_3_1751497925791.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6410e5f2803fcf0013284166/screenshot16_9_1751497928055.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6410e5f2803fcf0013284166/poster16_9_1751497927230.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1986-05-16T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5e94c2b1dfd9d6001ad86a7a", "seriesID": "5e94c2b0dfd9d6001ad86a71", "slug": "total-recall-1990-1-1", "name": "Total Recall", "summary": "It's 2084 A.D. and <PERSON> is haunted by recurring dreams about journeying to Earths closest planetary neighbor, Mars.", "description": "It's 2084 A.D. and <PERSON> is haunted by recurring dreams about journeying to Earths closest planetary neighbor, Mars.", "duration": 9000000, "originalContentDuration": 6855000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5e94c2b1dfd9d6001ad86a7a/screenshot16_9_1751498281580.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5e94c2b1dfd9d6001ad86a7a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5e94c2b1dfd9d6001ad86a7a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5e94c2b1dfd9d6001ad86a7a/poster_1751498282194.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5e94c2b1dfd9d6001ad86a7a/screenshot4_3_1751498281867.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5e94c2b1dfd9d6001ad86a7a/screenshot16_9_1751498281580.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5e94c2b1dfd9d6001ad86a7a/poster16_9_1751498281446.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1990-06-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5ab2fb91b71f50edcf89a94f", "seriesID": "5ab2fb90b71f50edcf89a94a", "slug": "trading-places-1983-1-1", "name": "Trading Places", "summary": "The story of a down-and-out con artist who trades lifestyles with a well-to-do investor.", "description": "The story of a down-and-out con artist who trades lifestyles with a well-to-do investor.", "duration": 9000000, "originalContentDuration": 6990000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5ab2fb91b71f50edcf89a94f/screenshot16_9_1751498282891.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Comedy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5ab2fb91b71f50edcf89a94f/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/5ab2fb91b71f50edcf89a94f/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5ab2fb91b71f50edcf89a94f/poster_1751498281751.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5ab2fb91b71f50edcf89a94f/screenshot4_3_1751498282382.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5ab2fb91b71f50edcf89a94f/screenshot16_9_1751498282891.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5ab2fb91b71f50edcf89a94f/poster16_9_1751498282876.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Elliott", "Don <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1983-01-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "590bfa5b114615beb3524f5a", "seriesID": "590bfa5b114615beb3524ef3", "slug": "witness-1985-1-1", "name": "Witness", "summary": "When a young Amish woman and her son get caught up in the murder of an undercover narcotic's agent, their saviour turns out to be hardened Philadelphia detective <PERSON>.", "description": "When a young Amish woman and her son get caught up in the murder of an undercover narcotic's agent, their saviour turns out to be hardened Philadelphia detective <PERSON>.", "duration": 9000000, "originalContentDuration": 6750000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/590bfa5b114615beb3524f5a/screenshot16_9_1751499375138.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/590bfa5b114615beb3524f5a/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/590bfa5b114615beb3524f5a/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/590bfa5b114615beb3524f5a/poster_1751499373118.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/590bfa5b114615beb3524f5a/screenshot4_3_1751499371595.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/590bfa5b114615beb3524f5a/screenshot16_9_1751499375138.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/590bfa5b114615beb3524f5a/poster16_9_1751499374254.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "originalReleaseDate": "1985-01-01T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "628e78837d8af3001a653419", "seriesID": "628e787f7d8af3001a653411", "slug": "air-force-one-1997-1-1", "name": "Air Force One", "summary": "When Russian neo-nationalists hijack Air Force One, the world's most secure aircraft, the President is faced with the decision to give in to terrorist demands or sacrifice the country's dignity and the lives of his wife and daughter.", "description": "When Russian neo-nationalists hijack Air Force One, the world's most secure aircraft, the President is faced with the decision to give in to terrorist demands or sacrifice the country's dignity and the lives of his wife and daughter.", "duration": 9900000, "originalContentDuration": 7485000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/628e78837d8af3001a653419/screenshot16_9_1749681722007.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/628e78837d8af3001a653419/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/628e78837d8af3001a653419/poster_1749681722572.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/628e78837d8af3001a653419/screenshot4_3_1749681722158.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/628e78837d8af3001a653419/screenshot16_9_1749681722007.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/628e78837d8af3001a653419/poster16_9_1749681722067.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1997-07-25T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "682e61fa00cabfe6fe77d6a2", "seriesID": "682e61f800cabfe6fe77d67b", "slug": "enough-2002-1-1", "name": "Enough", "summary": "An abused wife on the run from her violent husband finally decides she's had enough.", "description": "An abused wife on the run from her violent husband finally decides she's had enough.", "duration": 9000000, "originalContentDuration": 6930000, "allotment": 9000, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/682e61fa00cabfe6fe77d6a2/screenshot16_9_1749229561756.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/682e61fa00cabfe6fe77d6a2/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/682e61fa00cabfe6fe77d6a2/poster_1749229562246.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/682e61fa00cabfe6fe77d6a2/screenshot4_3_1749229562076.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/682e61fa00cabfe6fe77d6a2/screenshot16_9_1749229561756.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/682e61fa00cabfe6fe77d6a2/poster16_9_1749229562733.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2002-05-23T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "64c013550a21a300132add4a", "seriesID": "64c013500a21a300132add27", "slug": "a-few-good-men-1992-1-1", "name": "A Few Good Men", "summary": "<PERSON> stars as a brash Navy lawyer who's teamed with a gung-ho litigator (<PERSON><PERSON>) in a politically explosive murder case, defending two Marines accused of killing a fellow soldier", "description": "<PERSON> stars as a brash Navy lawyer who's teamed with a gung-ho litigator (<PERSON><PERSON>) in a politically explosive murder case, defending two Marines accused of killing a fellow soldier", "duration": ********, "originalContentDuration": 8295000, "allotment": 10800, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/64c013550a21a300132add4a/screenshot16_9_1749233883333.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/64c013550a21a300132add4a/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/64c013550a21a300132add4a/poster_1749233881732.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/64c013550a21a300132add4a/screenshot4_3_1749233881362.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/64c013550a21a300132add4a/screenshot16_9_1749233883333.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/64c013550a21a300132add4a/poster16_9_1749233882947.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1992-12-11T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5bac1a333a849c313518c2ba", "seriesID": "5bac1a323a849c313518c2b1", "slug": "flatliners-1-1", "name": "Flatliners", "summary": "Are you afraid to die? <PERSON><PERSON><PERSON> isn't. He's an ambitious, charismatic medical student who persuades classmates <PERSON> and <PERSON> to take part in a reckless experiment.  Rated: R", "description": "Are you afraid to die? <PERSON><PERSON><PERSON> isn't. He's an ambitious, charismatic medical student who persuades classmates <PERSON> and <PERSON> to take part in a reckless experiment.  Rated: R", "duration": 9000000, "originalContentDuration": 6870000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5bac1a333a849c313518c2ba/screenshot16_9_1751390286568.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Sci-Fi & Fantasy", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5bac1a333a849c313518c2ba/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5bac1a333a849c313518c2ba/poster_1751390284665.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5bac1a333a849c313518c2ba/screenshot4_3_1751390286039.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5bac1a333a849c313518c2ba/screenshot16_9_1751390286568.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5bac1a333a849c313518c2ba/poster16_9_1751390285351.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1990-08-10T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "682f79d21426f2773e96a71e", "seriesID": "682f79d01426f2773e96a69f", "slug": "the-girl-with-the-dragon-tattoo-2011-2011-1-1", "name": "The Girl with the Dragon Tattoo", "summary": "Journalist <PERSON><PERSON><PERSON> is aided in his search for a woman who has been missing for forty years by <PERSON><PERSON><PERSON>, a young computer hacker.", "description": "Journalist <PERSON><PERSON><PERSON> is aided in his search for a woman who has been missing for forty years by <PERSON><PERSON><PERSON>, a young computer hacker.", "duration": 12600000, "originalContentDuration": 9495000, "allotment": 12600, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/682f79d21426f2773e96a71e/screenshot16_9_1749682087040.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/682f79d21426f2773e96a71e/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/682f79d21426f2773e96a71e/poster_1749682086594.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/682f79d21426f2773e96a71e/screenshot4_3_1749682088086.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/682f79d21426f2773e96a71e/screenshot16_9_1749682087040.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/682f79d21426f2773e96a71e/poster16_9_1749682086117.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "directors": ["<PERSON>"], "producers": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "2011-12-02T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5c0f16fa741fbc7c3ea9007c", "seriesID": "5c0f16fa741fbc7c3ea90073", "slug": "in-the-line-of-fire-1-1", "name": "In the Line of Fire", "summary": "A gripping, gut-wrenching thriller that delivers suspense in almost unbearable doses, IN THE LINE OF FIRE showcases <PERSON> at his finest. In a performance that won universal acclaim, <PERSON> stars as <PERSON>. Rated: R", "description": "A Secret Service agent (<PERSON>) is taunted by calls from a would-be killer (<PERSON>) who has detailed information about the agent - including the fact that he failed to save President <PERSON> from assassination. Rated: R", "duration": 9900000, "originalContentDuration": 7740000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5c0f16fa741fbc7c3ea9007c/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Thriller", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5c0f16fa741fbc7c3ea9007c/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5c0f16fa741fbc7c3ea9007c/poster.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5c0f16fa741fbc7c3ea9007c/screenshot4_3.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5c0f16fa741fbc7c3ea9007c/screenshot16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5c0f16fa741fbc7c3ea9007c/poster16_9.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1993-07-09T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5baab377032d8524ac98e74c", "seriesID": "5baab376032d8524ac98e716", "slug": "the-net-1-1", "name": "The Net", "summary": "<PERSON>, <PERSON> and <PERSON> star in this hit thriller about a computer expert whose life is \"erased\" by a computer conspiracy. Rated: PG-13", "description": "<PERSON>, <PERSON> and <PERSON> star in this hit thriller about a computer expert whose life is \"erased\" by a computer conspiracy. Rated: PG-13", "duration": 9000000, "originalContentDuration": 6870000, "allotment": 9000, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5baab377032d8524ac98e74c/screenshot16_9_1751330162248.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5baab377032d8524ac98e74c/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5baab377032d8524ac98e74c/poster_1751386145779.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5baab377032d8524ac98e74c/screenshot4_3_1751330163237.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5baab377032d8524ac98e74c/screenshot16_9_1751330162248.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5baab377032d8524ac98e74c/poster16_9_1751386146683.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1995-07-28T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "6487615e5348f4001a0cdc5b", "seriesID": "6487615a5348f4001a0cdc38", "slug": "the-patriot-2000-2000-1-1", "name": "The Patriot (2000)", "summary": "In 1776 South Carolina, widower and legendary war hero <PERSON> (<PERSON>) finds himself thrust into the midst of the American Revolutionary War as he helplessly watches his family torn apart by the savage forces of the British Redcoats.", "description": "In 1776 South Carolina, widower and legendary war hero <PERSON> (<PERSON>) finds himself thrust into the midst of the American Revolutionary War as he helplessly watches his family torn apart by the savage forces of the British Redcoats.", "duration": 12600000, "originalContentDuration": 9900000, "allotment": 12600, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6487615e5348f4001a0cdc5b/screenshot16_9_1749073682929.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6487615e5348f4001a0cdc5b/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6487615e5348f4001a0cdc5b/poster_1749073684574.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6487615e5348f4001a0cdc5b/screenshot4_3_1749073682523.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6487615e5348f4001a0cdc5b/screenshot16_9_1749073682929.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6487615e5348f4001a0cdc5b/poster16_9_1749073682167.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "2000-06-28T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5b36cb6cad1b6cc6521590f9", "seriesID": "5b36cb6cad1b6cc6521590f5", "slug": "philadelphia-1993-1-1", "name": "Philadelphia", "summary": "Hailed as a landmark film that dazzles with deep emotion and exceptional acting, PHILADELPHIA stars <PERSON> and <PERSON><PERSON> as two competing lawyers who join forces to sue a prestigious law firm for AIDS discrimination. Rating: PG-13", "description": "Hailed as a landmark film that dazzles with deep emotion and exceptional acting, PHILADELPHIA stars <PERSON> and <PERSON><PERSON> as two competing lawyers who join forces to sue a prestigious law firm for AIDS discrimination. Rating: PG-13", "duration": 9900000, "originalContentDuration": 7545000, "allotment": 9900, "rating": "PG-13", "featuredImage": {"path": "https://images.pluto.tv/episodes/5b36cb6cad1b6cc6521590f9/screenshot16_9_1749479761535.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Drama", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5b36cb6cad1b6cc6521590f9/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5b36cb6cad1b6cc6521590f9/poster_1749479761980.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5b36cb6cad1b6cc6521590f9/screenshot4_3_1749479761768.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5b36cb6cad1b6cc6521590f9/screenshot16_9_1749479761535.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5b36cb6cad1b6cc6521590f9/poster16_9_1749479762136.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>"], "originalReleaseDate": "1993-12-10T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "5b4fd138380218bf4e4707fa", "seriesID": "5b4fd137380218bf4e4707f1", "slug": "the-professional-1-1", "name": "The Professional", "summary": "The mysterious <PERSON> (<PERSON>) is New York's top hitman. When his next-door neighbors are murdered, <PERSON> becomes the unwilling guardian of the family's sole survivor - 12-year-old <PERSON><PERSON><PERSON> (<PERSON>).  Rating:R", "description": "The mysterious <PERSON> (<PERSON>) is New York's top hitman. When his next-door neighbors are murdered, <PERSON> becomes the unwilling guardian of the family's sole survivor - 12-year-old <PERSON><PERSON><PERSON> (<PERSON>).  Rating:R", "duration": 9000000, "originalContentDuration": 6570000, "allotment": 9000, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5b4fd138380218bf4e4707fa/screenshot16_9_1749486602899.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5b4fd138380218bf4e4707fa/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5b4fd138380218bf4e4707fa/poster_1749486602723.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5b4fd138380218bf4e4707fa/screenshot4_3_1749486602092.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5b4fd138380218bf4e4707fa/screenshot16_9_1749486602899.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5b4fd138380218bf4e4707fa/poster16_9_1749486602175.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "originalReleaseDate": "1994-09-14T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "5c0f16ef741fbc7c3ea8fe7e", "seriesID": "5c0f16ef741fbc7c3ea8fe75", "slug": "the-quick-and-the-dead-1-1", "name": "The Quick and the Dead", "summary": "In this edgy and darkly humorous Western, a mysterious young woman rides into the lawless town of Redemption to settle an old score that has haunted her since she was a child. She becomes swept up in a deadly quick-draw tournament. Rated: R", "description": "A mysterious woman gunslinger, <PERSON> (<PERSON>), saunters into the town of Redemption looking for revenge against the town's sadistic mayor, <PERSON><PERSON> (<PERSON>), who is in the midst of organizing a quick-draw tournament. Rated: R", "duration": 8100000, "originalContentDuration": 6315000, "allotment": 8100, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/5c0f16ef741fbc7c3ea8fe7e/screenshot16_9_1748027889807.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Western", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/5c0f16ef741fbc7c3ea8fe7e/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/5c0f16ef741fbc7c3ea8fe7e/poster_1748027889615.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/5c0f16ef741fbc7c3ea8fe7e/screenshot4_3_1748027889762.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/5c0f16ef741fbc7c3ea8fe7e/screenshot16_9_1748027889807.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/5c0f16ef741fbc7c3ea8fe7e/poster16_9_1748027889749.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON> Blossom", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>"], "producers": ["<PERSON>", "<PERSON>", "<PERSON>"], "originalReleaseDate": "1995-02-10T00:00:00Z"}, "avail": {"startDate": "2025-07-01T07:00:00Z"}, "cc": true}, {"_id": "6707f89ab99f8500138e904b", "seriesID": "6707f898b99f8500138e9025", "slug": "sleepless-in-seattle-1993-1-1", "name": "Sleepless in Seattle", "summary": "<PERSON> and <PERSON> star in an enchanting romance about a widower whose son convinces him to find new love and the woman who answers his call.", "description": "<PERSON> and <PERSON> star in an enchanting romance about a widower whose son convinces him to find new love and the woman who answers his call.", "duration": 8100000, "originalContentDuration": 6315000, "allotment": 8100, "rating": "PG", "featuredImage": {"path": "https://images.pluto.tv/episodes/6707f89ab99f8500138e904b/screenshot16_9_1749681722538.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Romance", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6707f89ab99f8500138e904b/main.mpd"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6707f89ab99f8500138e904b/poster_1749681723535.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6707f89ab99f8500138e904b/screenshot4_3_1749681722459.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6707f89ab99f8500138e904b/screenshot16_9_1749681722538.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6707f89ab99f8500138e904b/poster16_9_1749681723431.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "directors": ["<PERSON>"], "writers": ["<PERSON>", "<PERSON>", "<PERSON>"], "producers": ["<PERSON>"], "originalReleaseDate": "1993-06-25T00:00:00Z"}, "avail": {}, "cc": true}, {"_id": "6840b809782266d453733b97", "seriesID": "6840b808782266d453733b72", "slug": "lethal-weapon-4-1998-1-1", "name": "Lethal Weapon 4", "summary": "<PERSON> and <PERSON> reteam as a pair of Los Angeles police detectives. Promoted to desk jobs to keep them out of trouble, the two cops join forces with a junior detective and a fast-talking private investigator to unravel a series of crimes.", "description": "<PERSON> and <PERSON> reteam as a pair of Los Angeles police detectives. Promoted to desk jobs to keep them out of trouble, the two cops join forces with a junior detective and a fast-talking private investigator to unravel a series of crimes.", "duration": 9900000, "originalContentDuration": 7650000, "allotment": 9900, "rating": "R", "featuredImage": {"path": "https://images.pluto.tv/episodes/6840b809782266d453733b97/screenshot16_9_1751349961666.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "genre": "Action & Adventure", "type": "movie", "seasonsNumbers": [], "stitched": {"paths": [{"type": "mpd", "path": "/stitch/dash/episode/6840b809782266d453733b97/main.mpd"}, {"type": "hls", "path": "/stitch/hls/episode/6840b809782266d453733b97/master.m3u8"}]}, "covers": [{"aspectRatio": "347:500", "url": "https://images.pluto.tv/episodes/6840b809782266d453733b97/poster_1751349961718.jpg?fill=blur&fit=fill&fm=jpg&h=1000&q=75&w=694"}, {"aspectRatio": "4:3", "url": "https://images.pluto.tv/episodes/6840b809782266d453733b97/screenshot4_3_1751349961278.jpg?fill=blur&fit=fill&fm=jpg&h=480&q=75&w=640"}, {"aspectRatio": "16:9", "url": "https://images.pluto.tv/episodes/6840b809782266d453733b97/screenshot16_9_1751349961666.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}], "poster16_9": {"path": "https://images.pluto.tv/episodes/6840b809782266d453733b97/poster16_9_1751349961563.jpg?fill=blur&fit=fill&fm=jpg&h=540&q=75&w=960"}, "clip": {"actors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jet Li"], "directors": ["<PERSON>"], "originalReleaseDate": "1998-07-10T00:00:00Z"}, "avail": {}, "cc": true}]