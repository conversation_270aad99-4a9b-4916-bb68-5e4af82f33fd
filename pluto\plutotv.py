# import requests

# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6ImVhYjE5MjEwLWQ4ZjUtNGMxNC05MzYxLWM3ZWRiNmU2ZDA2NyIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yH9PyhszzVJ7wh2ySE-U2HBNc8QFY3B-rqpxyzuhHe4',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# def get_params(offset: int, page: int):
#     return {
#         'offset': str(offset),
#         'page': str(page),
#     }



# response = requests.get(
#     'https://service-vod.clusters.pluto.tv/v4/vod/categories/6018833064d99100075a1e8a/items',
#     params=get_params(offset=1000, page=1),
#     headers=headers,
# )

# print(len(response.json().get('items')))

# with open('pluto.txt', 'w', encoding='utf-8') as f:
#     f.write(response.text)



# import requests

# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6ImVhYjE5MjEwLWQ4ZjUtNGMxNC05MzYxLWM3ZWRiNmU2ZDA2NyIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.alGdOpEbm7Ar5slDOcegCApwYP3BuuQzFcHljvQf-Ao',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# params = {
#     'limit': '100',
#     'offset': '100',
# }

# response = requests.get('https://service-watchlist-ga.prd.pluto.tv/v1/watchlist', params=params, headers=headers)
# print(response.text)

import requests
import json
from pathlib import Path

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
    'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjRmZGE2YjE2LWFjYzItNGVmYS04ZjY5LTE2ZjQwMGJmNzdmMCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Zc2UAs0WKRhG46q_1Fh01emZGsQIpKs3aK7lJv0k-8M',
    'cache-control': 'no-cache',
    'origin': 'https://pluto.tv',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://pluto.tv/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
}
path = Path(__file__).parent

response = requests.get('https://service-channels.clusters.pluto.tv/v2/guide/categories', headers=headers)
data = response.json().get('data')
with open(path / 'categories.json', 'w', encoding='utf-8') as f:
    f.write(json.dumps(data))

