{"18-jul-2025": [{"name": "A&E", "logo": "https://tvmap.com.br/images/ch/514.jpg", "programs": [{"title": "Casos Arquivados - Mortes no Velho Oeste : T1 EP2 - <PERSON>ão Matarás - Um Pecado no Oeste", "time": "01:55"}, {"title": "Casos Arquivados - Mortes no Pântano : T1 EP5 - Maldade na Louisiana", "time": "02:40"}, {"title": "Evidências Digitais de um Assassinato : T1 EP3 - Obsessão Digital", "time": "03:25"}, {"title": "Arquivo Morto: O DNA Não Falha : T1 EP8 - Rede de Mentiras Mortal", "time": "04:10"}, {"title": "As Primeiras 48 Horas - Minutos Críticos : T1 EP1 - O Caso que Me Assombra", "time": "04:55"}, {"title": "Guerra no Trânsito : T1 EP7 - Na Minha Rua Não", "time": "05:39"}, {"title": "Quem Dá Mai<PERSON>? : T15 EP24 - Por que a Galinha Atravessou a Estrada?", "time": "06:00"}, {"title": "Fichados - Primeira Vez na Prisão", "time": "06:24"}, {"title": "Quem Dá Mai<PERSON>? : T15 EP23 - Cinco Cartas e Nenhum Ás", "time": "06:29"}, {"title": "Quem Dá Mai<PERSON>? : T11 EP22 - Histórias de Tesouros", "time": "06:57"}, {"title": "Quem D<PERSON> Mai<PERSON>? : T11 EP23 - <PERSON><PERSON><PERSON>", "time": "07:20"}, {"title": "Quem D<PERSON>? : T11 EP1 - <PERSON><PERSON> Selvagens", "time": "07:43"}, {"title": "Quem Dá Mais? : T11 EP2 - Memórias da Pan Am", "time": "08:06"}, {"title": "Arquivo Morto: O DNA Não Falha : T1 EP7 - Assassinato por Dinheiro", "time": "08:29"}, {"title": "Assassinatos na Cidade do Pecado : T1 EP7 - Abandonado no Deserto", "time": "09:17"}, {"title": "As Confissões de Gypsy Rose : T1 EP2 - <PERSON> Corrida", "time": "10:06"}, {"title": "Clientes em Guerra : T2 EP11 - Drama no Drive Through", "time": "10:55"}, {"title": "Clientes em Guerra : T2 EP12 - Não Perca Essa Confusão", "time": "11:19"}, {"title": "Guerra no Trânsito : T2 EP5 - Conflito no Sinal Vermelho", "time": "11:43"}, {"title": "Guerra no Trânsito : T2 EP6 - Perseguição em Alta Velocidade", "time": "12:07"}, {"title": "Quem D<PERSON>? : T15 EP14 - <PERSON>ô", "time": "12:55"}, {"title": "Quem Dá Mai<PERSON>? : T15 EP24 - Por que a Galinha Atravessou a Estrada?", "time": "13:19"}, {"title": "Quem Dá Mai<PERSON>? : T15 EP23 - Cinco Cartas e Nenhum Ás", "time": "13:44"}, {"title": "Bosch : T1 EP8 - Altos e Baixos", "time": "14:08"}, {"title": "Terminus - Um Novo Começo", "time": "14:55"}, {"title": "Terminus - Um Novo Começo", "time": "14:55"}, {"title": "Carga Explosiva 3", "time": "16:28"}, {"title": "Carga Explosiva 3", "time": "16:28"}, {"title": "Quem Dá Mai<PERSON>? : T15 EP23 - Cinco Cartas e Nenhum Ás", "time": "18:14"}, {"title": "Quem D<PERSON> Mai<PERSON>? : T9 EP1 - Grande Compra Mexicana", "time": "18:38"}, {"title": "Quem Dá Mai<PERSON>? : T9 EP3 - Tartaruga Ninja na Meia-Idade", "time": "19:02"}, {"title": "Quem D<PERSON> Mai<PERSON>? : T9 EP9 - <PERSON>", "time": "19:26"}, {"title": "<PERSON>m D<PERSON> Mai<PERSON>? : T9 EP10 - <PERSON>", "time": "19:50"}]}, {"name": "AMC", "logo": "https://tvmap.com.br/images/ch/662.jpg", "programs": [{"title": "Kill Bill - Volume 2", "time": "01:55"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, E<PERSON>el<PERSON>", "time": "04:20"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, E<PERSON>el<PERSON>", "time": "04:20"}, {"title": "Planet Action", "time": "06:25"}, {"title": "Planet Action", "time": "08:30"}, {"title": "Planet Action", "time": "11:00"}, {"title": "Presságio", "time": "12:00"}, {"title": "Presságio", "time": "12:00"}, {"title": "O Escocês Voador", "time": "14:15"}, {"title": "O Escocês Voador", "time": "14:15"}, {"title": "O Homem da Máscara de Ferro", "time": "16:15"}, {"title": "O Homem da Máscara de Ferro", "time": "16:15"}, {"title": "Creed II", "time": "18:40"}, {"title": "Creed II", "time": "18:40"}]}, {"name": "Animal Planet", "logo": "https://tvmap.com.br/images/ch/470.jpg", "programs": [{"title": "Com Água até o Pescoço : T8 EP13 - <PERSON>qu<PERSON><PERSON> de Oculista", "time": "02:12"}, {"title": "Veterinários do Texas : T6 EP1 - Houston Ink", "time": "03:04"}, {"title": "Veterinário das Montanhas : T3 EP10 - Ombro Fraturado", "time": "03:56"}, {"title": "Pit Bulls e Condenados : T4 EP14 - Tempestade no Horizonte", "time": "04:47"}, {"title": "O Incrível Mundo Animal : T1 EP16 - Ovos Gigantes", "time": "05:35"}, {"title": "Piscinas Incríveis : T1 EP6 - Verde de Inveja", "time": "06:24"}, {"title": "Casas na Árvore : T8 EP6 - Reforma Surpresa", "time": "07:16"}, {"title": "Casas na Árvore : T8 EP6 - Reforma Surpresa", "time": "07:16"}, {"title": "Com Água até o Pescoço : T9 EP7 - Magia Submersa", "time": "08:05"}, {"title": "Com Água até o Pescoço : T9 EP1 - Começando do Zero", "time": "08:57"}, {"title": "Perdido na África - Entre Cobras e Leões", "time": "09:49"}, {"title": "Perdido na África - Entre Cobras e Leões", "time": "09:49"}, {"title": "Meu Gato Endiabrado : T6 EP5 - Amante de Ópera", "time": "11:39"}, {"title": "Meu Gato Endiabrado : T6 EP6 - Fome de Destruição", "time": "12:36"}, {"title": "Veterinário das Montanhas : T7 EP11 - O Mistério de <PERSON>", "time": "13:33"}, {"title": "Veterin<PERSON><PERSON> das Montanhas : T7 EP8 - Francesa com Problemas", "time": "14:28"}, {"title": "Veterinários do Texas : T5 EP5 - Garotos do Calendário", "time": "15:23"}, {"title": "Veterinários do Texas : T5 EP7 - O Caroço do Pato", "time": "16:18"}, {"title": "Pesca Mortal : T10 EP9 - <PERSON><PERSON><PERSON> em Treinamento", "time": "17:13"}, {"title": "Pesca Mortal : T10 EP9 - <PERSON><PERSON><PERSON> em Treinamento", "time": "17:13"}, {"title": "Pesca Mortal : T10 EP10 - A Filha do Pescador", "time": "18:06"}, {"title": "Com Água até o Pescoço : T7 EP11 - <PERSON>", "time": "18:59"}, {"title": "Com Água até o Pescoço : T8 EP5 - <PERSON> <PERSON><PERSON>", "time": "19:56"}, {"title": "Com Água até o Pescoço : T8 EP5 - <PERSON> <PERSON><PERSON>", "time": "19:56"}]}, {"name": "Arte 1", "logo": "https://tvmap.com.br/images/ch/600.jpg", "programs": [{"title": "Outras Vanguardas : Passoca", "time": "01:45"}, {"title": "A Aventura", "time": "02:45"}, {"title": "<PERSON><PERSON><PERSON> - Atemporal : <PERSON><PERSON><PERSON><PERSON>", "time": "05:15"}, {"title": "<PERSON>", "time": "06:15"}, {"title": "O Império de <PERSON>", "time": "07:45"}, {"title": "Arte 1 em Movimento", "time": "09:30"}, {"title": "<PERSON> e a Orquestra da Ópera de Paris - Concerto Inaugural", "time": "10:30"}, {"title": "Arte na Tecnologia : T3 EP2 - O Som e o Toque", "time": "12:00"}, {"title": "Tributo a Fela Kuti", "time": "13:00"}, {"title": "Território Flip : <PERSON><PERSON><PERSON><PERSON>", "time": "14:00"}, {"title": "Arte Makers : Arte e Oportunidade", "time": "14:15"}, {"title": "Fé na Batida : <PERSON><PERSON><PERSON>", "time": "14:30"}, {"title": "O Império de <PERSON>", "time": "15:00"}, {"title": "Arte Makers : Processos Criativos", "time": "16:45"}, {"title": "Amados Encontros : <PERSON>", "time": "17:00"}, {"title": "<PERSON><PERSON><PERSON> - Atemporal : <PERSON><PERSON><PERSON><PERSON>", "time": "18:00"}, {"title": "O Tempo e a Música - Villa-Lobos : T2 EP5 - O Folclore Sou Eu", "time": "19:00"}, {"title": "Outras Vanguardas : Passoca", "time": "19:30"}]}, {"name": "AXN", "logo": "https://tvmap.com.br/images/ch/505.jpg", "programs": [{"title": "Hawaii Five-0 : T10 EP9 - The Trunkless Tree of Kahilikolo", "time": "01:45"}, {"title": "CSI : T14 EP10 - Girls Gone Wild", "time": "02:40"}, {"title": "CSI : T14 EP11 - The Lost Reindeer", "time": "03:30"}, {"title": "CSI : T14 EP12 - Keep Calm and Carry-On", "time": "04:20"}, {"title": "CSI : T14 EP13 - <PERSON> Brakes", "time": "05:10"}, {"title": "NCIS: Los Angeles : T2 EP19 - Enemy Within", "time": "06:00"}, {"title": "NCIS: Los Angeles : T2 EP20 - The Job", "time": "06:50"}, {"title": "NCIS: Los Angeles : T2 EP21 - <PERSON>", "time": "07:40"}, {"title": "NCIS: Los Angeles : T2 EP22 - Plan B", "time": "08:30"}, {"title": "NCIS: Los Angeles : T2 EP19 - Enemy Within", "time": "09:20"}, {"title": "NCIS : T9 EP14 - Life Before His Eyes", "time": "10:10"}, {"title": "NCIS: Los Angeles : T4 EP9 - The Gold Standard", "time": "11:00"}, {"title": "NCIS: New Orleans : T1 EP3 - Breaking Brig", "time": "11:55"}, {"title": "CSI: Miami : T4 EP21 - Dead Air", "time": "12:50"}, {"title": "CSI: Miami : T4 EP22 - Open Water", "time": "13:45"}, {"title": "CSI: Miami : T4 EP23 - Shock", "time": "14:40"}, {"title": "CSI: Miami : T4 EP24 - Rampage", "time": "15:30"}, {"title": "CSI: Miami : T4 EP25 - One of Our Own", "time": "16:20"}, {"title": "CSI: Miami : T5 EP1 - Rio", "time": "17:10"}, {"title": "CSI: Miami : T5 EP2 - Going Under", "time": "18:00"}, {"title": "CSI: Miami : T5 EP3 - Death Pool 100", "time": "18:50"}, {"title": "CSI: Miami : T5 EP4 - If Looks Could Kill", "time": "19:40"}]}, {"name": "Band", "logo": "https://tvmap.com.br/images/ch/403.jpg", "programs": [{"title": "Band Esporte", "time": "01:55"}, {"title": "+ Info", "time": "02:50"}, {"title": "Jornal da Band", "time": "03:00"}, {"title": "Game Show BandBet", "time": "04:00"}, {"title": "<PERSON><PERSON> o quê?", "time": "05:00"}, {"title": "Profeta Vinícius <PERSON>", "time": "05:45"}, {"title": "Igreja Pentecostal Cristo para as Nações", "time": "06:00"}, {"title": "Jornal BandNews", "time": "06:45"}, {"title": "Valor da Vida", "time": "07:00"}, {"title": "AgroBand", "time": "08:00"}, {"title": "Bora Brasil", "time": "08:15"}, {"title": "Bora Brasil", "time": "08:15"}, {"title": "<PERSON><PERSON>", "time": "11:00"}, {"title": "<PERSON><PERSON> - De<PERSON>", "time": "12:00"}, {"title": "Os Donos da Bola", "time": "13:00"}, {"title": "<PERSON><PERSON>", "time": "14:30"}, {"title": "<PERSON><PERSON>", "time": "14:30"}, {"title": "Brasil Urgente", "time": "16:00"}, {"title": "Brasil Urgente", "time": "16:00"}, {"title": "Jornal da Band", "time": "19:20"}]}, {"name": "Band News", "logo": "https://tvmap.com.br/images/ch/531.jpg", "programs": [{"title": "Jo<PERSON> da Noite", "time": "02:00"}, {"title": "Madrugada BandNews", "time": "02:45"}, {"title": "Jo<PERSON>", "time": "03:00"}, {"title": "Economia pra Você", "time": "03:30"}, {"title": "1º Jornal", "time": "04:00"}, {"title": "1º Jornal", "time": "04:00"}, {"title": "Jornal BandNews", "time": "06:00"}, {"title": "Jornal Gente", "time": "08:00"}, {"title": "Radar BandNews", "time": "09:30"}, {"title": "Manhã BandNews", "time": "10:00"}, {"title": "BandNews no Meio do Dia", "time": "12:00"}, {"title": "BandNews no Meio do Dia", "time": "12:00"}, {"title": "Tarde BandNews", "time": "14:00"}, {"title": "Tarde BandNews", "time": "14:00"}, {"title": "Entre Nós", "time": "16:00"}, {"title": "Expresso BandNews", "time": "17:00"}, {"title": "O <PERSON> da Coisa", "time": "18:00"}, {"title": "Jornal BandNews", "time": "19:30"}]}, {"name": "BBC World News", "logo": "https://tvmap.com.br/images/ch/532.jpg", "programs": [{"title": "The Travel Show : My Great Malaysian Railway Adventure", "time": "02:55"}, {"title": "BBC News", "time": "04:00"}, {"title": "The Travel Show : My Great Malaysian Railway Adventure", "time": "05:55"}, {"title": "BBC News", "time": "06:30"}, {"title": "BBC News Now", "time": "08:00"}, {"title": "The Travel Show : My Great Malaysian Railway Adventure", "time": "09:55"}, {"title": "Business Today", "time": "10:30"}, {"title": "BBC News Now", "time": "10:45"}, {"title": "Verified Live", "time": "11:00"}, {"title": "Focus on Africa", "time": "13:30"}, {"title": "The World Today with <PERSON><PERSON>", "time": "14:00"}, {"title": "The Travel Show : My Great Malaysian Railway Adventure", "time": "15:55"}, {"title": "The Context", "time": "16:00"}, {"title": "The Context USA", "time": "17:00"}, {"title": "BBC World News America", "time": "18:00"}, {"title": "Newsnight", "time": "18:30"}, {"title": "The President's Path", "time": "19:30"}]}, {"name": "Bloomberg", "logo": "https://tvmap.com.br/images/ch/465.jpg", "programs": [{"title": "Bloomberg Daybreak: Europe", "time": "03:00"}, {"title": "Bloomberg Markets: Europe", "time": "04:00"}, {"title": "Bloomberg Markets: European Open", "time": "05:00"}, {"title": "Bloomberg Surveillance: Early Edition", "time": "06:00"}, {"title": "Bloomberg Surveillance", "time": "07:00"}, {"title": "Bloomberg Surveillance", "time": "07:00"}, {"title": "Bloomberg Markets: The Open", "time": "10:00"}, {"title": "Bloomberg Markets", "time": "11:00"}, {"title": "Bloomberg Technology", "time": "12:00"}, {"title": "Bloomberg Real Yield", "time": "13:00"}, {"title": "Bloomberg Markets", "time": "13:30"}, {"title": "Bloomberg: Balance of Power", "time": "14:00"}, {"title": "Bloomberg Businessweek", "time": "15:00"}, {"title": "Bloomberg: The Close", "time": "16:00"}, {"title": "Bloomberg: Balance of Power", "time": "18:00"}, {"title": "Bloomberg Wall Street Week", "time": "19:00"}, {"title": "The Deal with <PERSON> and <PERSON>", "time": "20:00"}]}, {"name": "Boa Vontade TV", "logo": "https://tvmap.com.br/images/ch/603.jpg", "programs": [{"title": "Momento Ecumênico de Elevação Espiritual", "time": "02:10"}, {"title": "Programa Boa Vontade, com Paiva Netto", "time": "02:35"}, {"title": "Mensagem de Paiva Netto", "time": "02:55"}, {"title": "Vamos Falar com Deus", "time": "03:10"}, {"title": "Momento Ecumênico de Oração", "time": "04:00"}, {"title": "Músicas que Elevam", "time": "04:10"}, {"title": "O Assunto É Jesus!", "time": "05:15"}, {"title": "<PERSON><PERSON><PERSON><PERSON> da Ave, <PERSON>!", "time": "05:50"}, {"title": "Mensagem de Paiva Netto", "time": "06:45"}, {"title": "O Poder da Fé Realizante", "time": "06:55"}, {"title": "Momento Ecumênico de Oração", "time": "07:00"}, {"title": "Caminhando com Jesus na LBV", "time": "08:05"}, {"title": "Programa Boa Vontade, com Paiva Netto", "time": "08:35"}, {"title": "Mensagem de Paiva Netto", "time": "08:50"}, {"title": "Comando da Esperança", "time": "09:10"}, {"title": "<PERSON><PERSON>", "time": "12:10"}, {"title": "Momento Ecumênico de Oração", "time": "13:00"}, {"title": "<PERSON><PERSON>e Entrevista", "time": "13:10"}, {"title": "Programa Religião de Deus, do Cristo e do Espírito Santo Responde", "time": "13:40"}, {"title": "Caminhando com Jesus na LBV", "time": "14:10"}, {"title": "SOS Português", "time": "14:40"}, {"title": "Viver <PERSON>", "time": "15:10"}, {"title": "Momento Ecumênico de Oração", "time": "16:00"}, {"title": "<PERSON><PERSON>", "time": "16:10"}, {"title": "Programa Boa Vontade, com Paiva Netto", "time": "16:35"}, {"title": "Mensagem de Paiva Netto", "time": "16:55"}, {"title": "O Assunto É Jesus!", "time": "17:10"}, {"title": "<PERSON><PERSON><PERSON><PERSON> da Ave, <PERSON>!", "time": "17:50"}, {"title": "Momento Ecumênico de Oração", "time": "19:00"}, {"title": "Caminhando com Jesus na LBV", "time": "19:10"}, {"title": "Programa Religião de Deus, do Cristo e do Espírito Santo Responde", "time": "19:30"}]}, {"name": "Canal Rural", "logo": "https://tvmap.com.br/images/ch/463.jpg", "programs": [{"title": "Televendas Marilda", "time": "01:00"}, {"title": "TV Verdade", "time": "03:00"}, {"title": "TV Verdade", "time": "03:00"}, {"title": "Cooper News SC", "time": "06:00"}, {"title": "Planeta Campo Entrevista", "time": "06:30"}, {"title": "Giro do Boi", "time": "07:00"}, {"title": "Planeta Campo", "time": "08:00"}, {"title": "Ganhando o Futuro", "time": "08:30"}, {"title": "Agro 360", "time": "09:00"}, {"title": "Televendas Marilda", "time": "09:30"}, {"title": "Televendas Marilda", "time": "09:30"}, {"title": "A Protagonista", "time": "11:00"}, {"title": "Ligados e Integrados - Vida no Campo", "time": "11:30"}, {"title": "Planeta Campo", "time": "12:00"}, {"title": "Mercado & Cia", "time": "12:30"}, {"title": "Em Busca da Árvore Perfeita", "time": "13:35"}, {"title": "Televendas Marilda", "time": "14:04"}, {"title": "Televendas Marilda", "time": "14:04"}, {"title": "Acima de Tudo o Amor", "time": "16:34"}, {"title": "Vale Agrícola", "time": "17:00"}, {"title": "Planeta Campo", "time": "18:00"}, {"title": "Ganhando o Futuro", "time": "18:15"}, {"title": "Rural Notícias", "time": "18:45"}, {"title": "Pecuária em Foco", "time": "19:45"}]}, {"name": "Cartoon Network", "logo": "https://tvmap.com.br/images/ch/524.jpg", "programs": [{"title": "Looney Tunes Cartoons", "time": "02:00"}, {"title": "Hora de Aventura", "time": "03:00"}, {"title": "Drama Total Kids", "time": "04:30"}, {"title": "Irmão do Jorel", "time": "07:00"}, {"title": "Os Jovens Titãs em Ação", "time": "07:35"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, o Otimista", "time": "09:00"}, {"title": "<PERSON><PERSON>", "time": "09:30"}, {"title": "Os Jovens Titãs em Ação", "time": "10:00"}, {"title": "Uma Aventura LEGO 2", "time": "11:00"}, {"title": "Irmão do Jorel", "time": "13:00"}, {"title": "Ursinhos em Curso", "time": "14:00"}, {"title": "Os Jovens Titãs em Ação", "time": "15:00"}, {"title": "Tiny Toons - Looniversidade", "time": "16:00"}, {"title": "Pinecone & Pony", "time": "16:45"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, o Otimista", "time": "17:00"}, {"title": "Ursinhos em Curso", "time": "18:00"}, {"title": "<PERSON> & <PERSON>", "time": "18:30"}, {"title": "Os Jovens Titãs em Ação", "time": "18:35"}, {"title": "Summer Memories", "time": "19:15"}, {"title": "Big Blue: O Grande Oceano : T1 - <PERSON> Cadeira da Equipe", "time": "19:30"}, {"title": "A Jornada Heroica do Valente Príncipe <PERSON>", "time": "19:45"}]}, {"name": "Cinemax", "logo": "https://tvmap.com.br/images/ch/504.jpg", "programs": [{"title": "Rota de Fuga 3 - O Resgate", "time": "02:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "03:36"}, {"title": "Cinemax Movie", "time": "05:27"}, {"title": "Os Especialistas", "time": "06:31"}, {"title": "Os Especialistas", "time": "06:31"}, {"title": "<PERSON><PERSON>", "time": "08:27"}, {"title": "Debi & Lóide 2", "time": "10:03"}, {"title": "<PERSON><PERSON>", "time": "11:59"}, {"title": "<PERSON><PERSON>", "time": "11:59"}, {"title": "<PERSON><PERSON>", "time": "13:39"}, {"title": "<PERSON><PERSON>", "time": "13:39"}, {"title": "Todo Mundo em Pânico", "time": "15:31"}, {"title": "<PERSON><PERSON>", "time": "17:07"}, {"title": "<PERSON><PERSON>", "time": "17:07"}, {"title": "Debi & Lóide 2", "time": "18:52"}, {"title": "Debi & Lóide 2", "time": "18:52"}]}, {"name": "Climatempo", "logo": "https://tvmap.com.br/images/ch/455.jpg", "programs": [{"title": "Caminhos do Brasil", "time": "03:30"}, {"title": "Foco Ambiental", "time": "04:00"}, {"title": "C3.NEWS", "time": "05:30"}, {"title": "Hora do Pescador", "time": "06:30"}, {"title": "Planeta Turismo", "time": "07:00"}, {"title": "Bom Dia Agro", "time": "08:00"}, {"title": "Atitude Sustentável", "time": "09:30"}, {"title": "C3.NEWS", "time": "10:30"}, {"title": "Adventurar", "time": "11:00"}, {"title": "Blindagem em Foco", "time": "11:30"}, {"title": "Sucesso no Campo", "time": "12:00"}, {"title": "Tempo e Dinheiro", "time": "12:30"}, {"title": "Vendo na TV", "time": "14:00"}, {"title": "Atitude Sustentável", "time": "14:30"}, {"title": "BR Acima de Tudo", "time": "15:00"}, {"title": "Remate <PERSON><PERSON>", "time": "16:00"}, {"title": "C3.NEWS", "time": "16:30"}, {"title": "Origem e Destino", "time": "17:00"}, {"title": "Adventurar", "time": "18:00"}, {"title": "Blindagem em Foco", "time": "18:30"}, {"title": "Vinhos.br", "time": "19:00"}, {"title": "Economia Circular", "time": "19:30"}]}, {"name": "CNT", "logo": "https://tvmap.com.br/images/ch/462.jpg", "programs": [{"title": "<PERSON><PERSON><PERSON>", "time": "03:00"}, {"title": "IURD - <PERSON><PERSON><PERSON> dos 70", "time": "03:30"}, {"title": "A Última Porta", "time": "04:00"}, {"title": "SOS Espiritual", "time": "05:30"}, {"title": "Mensagem do Bispo Macedo - Templo", "time": "06:00"}, {"title": "Prosperidade com Deus", "time": "07:01"}, {"title": "<PERSON><PERSON><PERSON>", "time": "08:00"}, {"title": "Ponto de Luz", "time": "08:30"}, {"title": "SOS Espiritual", "time": "09:00"}, {"title": "Em Busca do Amor", "time": "10:30"}, {"title": "Nosso Tempo", "time": "11:00"}, {"title": "Prosperidade com Deus", "time": "11:30"}, {"title": "Palavra Amiga", "time": "12:00"}, {"title": "Problemas e Soluções", "time": "13:00"}, {"title": "<PERSON><PERSON> da Crise", "time": "13:30"}, {"title": "SOS Espiritual", "time": "14:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "14:30"}, {"title": "Inteligência e Fé - Bispo Renato", "time": "15:00"}, {"title": "Prosperidade com Deus", "time": "16:01"}, {"title": "IURD - <PERSON><PERSON><PERSON> dos 70", "time": "16:30"}, {"title": "<PERSON><PERSON> da Crise", "time": "17:00"}, {"title": "Oração da Fogueira da Santa", "time": "18:30"}, {"title": "IURD - <PERSON><PERSON><PERSON> dos 70", "time": "19:00"}, {"title": "<PERSON><PERSON> da Crise", "time": "19:30"}]}, {"name": "Comedy Central", "logo": "https://tvmap.com.br/images/ch/606.jpg", "programs": [{"title": "Key & Peele : T3 EP6 - A Cama Que<PERSON>da <PERSON> Wendell", "time": "02:25"}, {"title": "Idiotando : T3 EP11 - <PERSON><PERSON>", "time": "02:45"}, {"title": "Idiotando : T3 EP6 - <PERSON><PERSON><PERSON><PERSON>", "time": "03:10"}, {"title": "Idiotando : T3 EP5 - <PERSON><PERSON><PERSON> da Adrenalina", "time": "03:30"}, {"title": "Idiotando : T3 EP8 - <PERSON><PERSON>morati<PERSON>", "time": "03:50"}, {"title": "Idiotando : T3 EP2 - <PERSON><PERSON><PERSON>", "time": "04:15"}, {"title": "Papai em Apuros : T1 EP2 - <PERSON><PERSON>", "time": "05:15"}, {"title": "Papai em Apuros : T1 EP3 - Papai Treinador", "time": "05:35"}, {"title": "Papai em Apuros : T1 EP4 - Papai Ressurge dos Mortos", "time": "06:00"}, {"title": "O Rei do Bairro : T7 EP13 - Cantada do Macaco", "time": "06:20"}, {"title": "O Rei do Bairro : T7 EP14 - <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "time": "06:45"}, {"title": "Frasier : T7 EP12 - RD<PERSON>ER", "time": "07:05"}, {"title": "Frasier : T7 EP13 - They're Playing Our Song", "time": "07:25"}, {"title": "Frasier : T7 EP14 - <PERSON> on Campus", "time": "07:50"}, {"title": "Frasier : T7 EP15 - Out With Dad", "time": "08:15"}, {"title": "Becker : T1 EP4 - <PERSON><PERSON><PERSON>", "time": "08:35"}, {"title": "Becker : T1 EP5 - <PERSON><PERSON> com Becker", "time": "09:00"}, {"title": "Becker : T1 EP6 - <PERSON>m <PERSON>, Deus Ri", "time": "09:20"}, {"title": "O Rei do Bairro : T7 EP20 - É o Inferno", "time": "09:40"}, {"title": "O Rei do Bairro : T7 EP22 - Compra Curiosa", "time": "10:25"}, {"title": "O Rei do Bairro : T8 EP2 - Discórdia Vocal", "time": "10:50"}, {"title": "O Rei do Bairro : T7 EP15 - Desconstruindo a Carrie", "time": "11:15"}, {"title": "O Rei do Bairro : T7 EP16 - A Lista Negra", "time": "11:35"}, {"title": "Becker : T1 EP10 - Politicamente Correto", "time": "11:55"}, {"title": "Becker : T1 EP11 - Falta de Inspiração", "time": "12:20"}, {"title": "Becker : T1 EP12 - Amor! Mentiras! Sangue!", "time": "12:45"}, {"title": "As Regras do Amor : T5 EP1 - <PERSON><PERSON><PERSON>", "time": "13:05"}, {"title": "As Regras do Amor : T5 EP2 - O Banco", "time": "13:30"}, {"title": "A Boa Vizinhança : T3 EP16 - Bem-Vindos ao Teste", "time": "13:50"}, {"title": "A Boa Vizinhança : T3 EP17 - Bem-Vindos à Invasão", "time": "14:10"}, {"title": "O Rei do Bairro : T8 EP13 - Jogando e Falando", "time": "14:30"}, {"title": "O Rei do Bairro : T8 EP14 - Apartamento Complexo", "time": "14:50"}, {"title": "O Rei do Bairro : T8 EP10 - Touros Selvagens", "time": "15:15"}, {"title": "O Rei do Bairro : T8 EP16 - <PERSON><PERSON>", "time": "15:35"}, {"title": "As Regras do Amor : T5 EP11 - Recusando o Orçamento", "time": "16:00"}, {"title": "As Regras do Amor : T5 EP12 - <PERSON><PERSON><PERSON><PERSON>", "time": "16:20"}, {"title": "A Boa Vizinhança : T1 EP8 - Bem-Vindos ao Dia de Ação de Graças", "time": "16:45"}, {"title": "A Boa Vizinhança : T1 EP9 - Bem-Vindos ao Jantar para a Convidada", "time": "17:05"}, {"title": "Becker : T1 EP14 - <PERSON> <PERSON>... <PERSON>", "time": "17:25"}, {"title": "Becker : T1 EP15 - Arque com Suas Escolhas", "time": "17:45"}, {"title": "Becker : T1 EP16 - Regras e Limites", "time": "18:10"}, {"title": "Frasier : T4 EP18 - Ham Radio", "time": "18:30"}, {"title": "Frasier : T7 EP10 - Back Talk", "time": "18:50"}, {"title": "Do que Riem? : T1 EP7 - Do que Riem os Garçons?", "time": "19:10"}, {"title": "Comedy Central Stand-Up : T1 EP25 - <PERSON><PERSON><PERSON>; <PERSON>", "time": "19:30"}, {"title": "Awkward. : T4 EP21 - <PERSON><PERSON><PERSON><PERSON>ão", "time": "19:50"}]}, {"name": "Cultura", "logo": "https://tvmap.com.br/images/ch/397.jpg", "programs": [{"title": "Jornal da Cultura", "time": "02:30"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "03:30"}, {"title": "<PERSON> - Amores e Guerras", "time": "04:00"}, {"title": "Inglês com Música", "time": "05:00"}, {"title": "Opinião", "time": "06:30"}, {"title": "Cocoricó", "time": "07:00"}, {"title": "O Diário <PERSON>", "time": "07:15"}, {"title": "<PERSON><PERSON><PERSON> da Cultura", "time": "08:00"}, {"title": "<PERSON><PERSON><PERSON> da Cultura", "time": "08:00"}, {"title": "<PERSON><PERSON> da Tarde", "time": "12:00"}, {"title": "Propaganda Eleitoral", "time": "13:00"}, {"title": "Oi, Duggee!", "time": "13:10"}, {"title": "<PERSON>", "time": "13:20"}, {"title": "Um Herói do Coração", "time": "13:30"}, {"title": "Masha e o Urso", "time": "13:45"}, {"title": "<PERSON><PERSON><PERSON> da Cultura", "time": "14:00"}, {"title": "<PERSON><PERSON><PERSON> da Cultura", "time": "14:00"}, {"title": "<PERSON><PERSON> da Mônica", "time": "16:45"}, {"title": "O Mundo de Mia", "time": "17:00"}, {"title": "Irmão do Jorel", "time": "18:00"}, {"title": "<PERSON>, o <PERSON><PERSON><PERSON>", "time": "18:10"}, {"title": "Metrópolis", "time": "18:20"}, {"title": "O Mundo de Beakman", "time": "18:30"}, {"title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "time": "19:00"}]}, {"name": "<PERSON>urta!", "logo": "https://tvmap.com.br/images/ch/517.jpg", "programs": [{"title": "Noite e Dia - Lima Barreto, Obra & Vida", "time": "02:00"}, {"title": "Revoluções Sexuais : T1 EP2 - Reinventando o Amor", "time": "04:00"}, {"title": "Zélia - Memórias e Saudades : T1 EP2 - Memórias do Nascimento do Amor", "time": "05:00"}, {"title": "<PERSON>ós, Documentaristas : <PERSON><PERSON>", "time": "06:00"}, {"title": "Intérpretes do Brasil : <PERSON><PERSON>, <PERSON><PERSON> <PERSON>", "time": "06:30"}, {"title": "Coreografia - O Desenho da Dança no Brasil : <PERSON><PERSON><PERSON>", "time": "07:00"}, {"title": "Diretores de Arte : <PERSON>", "time": "08:00"}, {"title": "Companhias do Teatro Brasileiro : Teatro Oficina", "time": "08:30"}, {"title": "Grandes Cenas : T1 - <PERSON> Guardião", "time": "09:00"}, {"title": "Na Trilha do Cinema : <PERSON><PERSON><PERSON>", "time": "09:30"}, {"title": "O Cinema das Mulheres", "time": "10:30"}, {"title": "Revoluções Sexuais : T1 EP2 - Reinventando o Amor", "time": "12:00"}, {"title": "Zélia - Memórias e Saudades : T1 EP2 - Memórias do Nascimento do Amor", "time": "13:00"}, {"title": "Consciência³ : A Consciência do Outro", "time": "14:00"}, {"title": "Incertezas Críticas : T2 EP8 - <PERSON>", "time": "15:30"}, {"title": "Noite e Dia - Lima Barreto, Obra & Vida", "time": "16:00"}, {"title": "Alma Imoral : Ruptura e Continuidade", "time": "18:00"}, {"title": "Máfia e Bancos : T1 EP1 - A Era dos Pioneiros", "time": "19:00"}]}, {"name": "Deutsche Welle (DW-TV)", "logo": "https://tvmap.com.br/images/ch/573.jpg", "programs": [{"title": "A fondo", "time": "02:15"}, {"title": "Enlaces: <PERSON><PERSON><PERSON> abierta al mundo digital", "time": "03:00"}, {"title": "RPM", "time": "03:30"}, {"title": "DW Noticias", "time": "04:00"}, {"title": "Economía", "time": "04:03"}, {"title": "Enlaces: <PERSON><PERSON><PERSON> abierta al mundo digital", "time": "05:30"}, {"title": "Global es", "time": "06:03"}, {"title": "<PERSON><PERSON><PERSON> latina, mujeres inspiradoras de América Latina", "time": "06:30"}, {"title": "DW Noticias", "time": "07:00"}, {"title": "Patrimonio mundial : <PERSON><PERSON><PERSON>, Fasto Principesco y Artisticos Jardines, República Checa", "time": "07:03"}, {"title": "A fondo", "time": "07:15"}, {"title": "Enfoque Europa: países, gentes, destinos", "time": "08:03"}, {"title": "Cultura.21", "time": "08:30"}, {"title": "<PERSON><PERSON><PERSON><PERSON> estoy", "time": "09:03"}, {"title": "Global es", "time": "09:30"}, {"title": "A fondo", "time": "10:15"}, {"title": "<PERSON><PERSON><PERSON> latina, mujeres inspiradoras de América Latina", "time": "11:03"}, {"title": "Hecho en Alemania", "time": "11:30"}, {"title": "<PERSON><PERSON><PERSON><PERSON> estoy", "time": "12:03"}, {"title": "DW Noticias", "time": "13:00"}, {"title": "Enfoque Europa: países, gentes, destinos", "time": "13:03"}, {"title": "Enlaces: <PERSON><PERSON><PERSON> abierta al mundo digital", "time": "13:30"}, {"title": "Patrimonio mundial : <PERSON><PERSON><PERSON>, Fasto Principesco y Artisticos Jardines, República Checa", "time": "14:03"}, {"title": "A fondo", "time": "14:15"}, {"title": "Eco Latinoamérica", "time": "15:30"}, {"title": "DW Noticias", "time": "16:00"}, {"title": "Zona docu", "time": "16:03"}, {"title": "Patrimonio mundial : La Alhambra: <PERSON><PERSON><PERSON>, España", "time": "18:03"}, {"title": "DocFilm : Inmortalidad: ¿Vida eterna con biohacking?", "time": "18:15"}, {"title": "DW Noticias", "time": "19:00"}, {"title": "¿Cómo te afecta?", "time": "19:30"}]}, {"name": "Discovery Channel", "logo": "https://tvmap.com.br/images/ch/468.jpg", "programs": [{"title": "Operação Fronteira - Brasil : T2 EP2 - Muambas na Cabriteira", "time": "02:12"}, {"title": "Operação Fronteira - Brasil : T2 EP3 - 2 Milhões em Celulares", "time": "02:38"}, {"title": "Operação Fronteira - Brasil : T2 EP4 - <PERSON>", "time": "03:04"}, {"title": "Operação Fronteira - Brasil : T2 EP5 - Capulho", "time": "03:30"}, {"title": "Operação Fronteira - Brasil : T2 EP6 - Colegas do Ônibus", "time": "03:56"}, {"title": "Operação Fronteira - Brasil : T2 EP7 - <PERSON><PERSON><PERSON> de Rinha", "time": "04:20"}, {"title": "Operação Fronteira - Brasil : T2 EP9 - 20 Kg de Maconha", "time": "05:10"}, {"title": "Operação Fronteira - Brasil : T2 EP10 - Perseguição", "time": "05:36"}, {"title": "Encontro Selvagem com Coyote Peterson : T1 EP10 - No Covil da Sucuri", "time": "06:00"}, {"title": "África : T1 EP2 - <PERSON>", "time": "06:27"}, {"title": "La<PERSON><PERSON>, Pelados e Ilhados : T1 EP5 - <PERSON><PERSON> Aguento Mai<PERSON>", "time": "07:17"}, {"title": "La<PERSON><PERSON>, Pelados e Ilhados : T1 EP5 - <PERSON><PERSON> Aguento Mai<PERSON>", "time": "07:17"}, {"title": "Largados, Pelados e Ilhados : T1 EP6 - Porcos e Crocodilos", "time": "08:06"}, {"title": "Desafio em Dose Dupla : T6 EP1 - P<PERSON>adelo <PERSON>", "time": "08:56"}, {"title": "Desafio em Dose Dupla - Brasil : T2 EP5 - Atolados na Lama", "time": "09:45"}, {"title": "Desafio em Dose Dupla - Brasil : T2 EP5 - Atolados na Lama", "time": "09:45"}, {"title": "<PERSON> - Aventura em Família", "time": "10:34"}, {"title": "Vida Remota : T10 - Preparados para o Inverno?", "time": "12:09"}, {"title": "Vida Remota : T10 - Preparados para o Inverno?", "time": "12:09"}, {"title": "La<PERSON><PERSON>, Pelados e Ilhados : T1 EP5 - <PERSON><PERSON> Aguento Mai<PERSON>", "time": "13:51"}, {"title": "Largados, Pelados e Ilhados : T1 EP6 - Porcos e Crocodilos", "time": "14:41"}, {"title": "Largados, Pelados e Ilhados : T1 EP6 - Porcos e Crocodilos", "time": "14:41"}, {"title": "Desafio em Dose Dupla : T6 EP1 - P<PERSON>adelo <PERSON>", "time": "15:31"}, {"title": "Desafio em Dose Dupla - Brasil : T2 EP5 - Atolados na Lama", "time": "16:21"}, {"title": "<PERSON> - Aventura em Família", "time": "17:11"}, {"title": "<PERSON> - Aventura em Família", "time": "17:11"}, {"title": "Largados e Pelados : T2 EP4 - Paraíso Perdido", "time": "18:50"}, {"title": "Largados e Pelados : T2 EP7 - Bolívia Implacável", "time": "19:39"}]}, {"name": "Discovery Home & Health", "logo": "https://tvmap.com.br/images/ch/469.jpg", "programs": [{"title": "90 Dias na Cama - Antes dos 90 Dias : T4 EP13 - O Princípio do Prazer", "time": "01:53"}, {"title": "<PERSON><PERSON><PERSON> - Itália", "time": "02:35"}, {"title": "<PERSON><PERSON><PERSON> - Como Eles Estão Agora? : T3 EP4 - <PERSON>", "time": "04:06"}, {"title": "<PERSON><PERSON><PERSON> - Como Eles Estão Agora? : T3 EP4 - <PERSON>", "time": "04:06"}, {"title": "Reforma dos Sonhos : T1 EP4 - Primos no México", "time": "05:38"}, {"title": "Reforma dos Sonhos : T1 EP7 - Uma Extensão Inspirada", "time": "06:00"}, {"title": "Imóveis que <PERSON>abem no Bolso : T2 EP4 - Sonhos em Beantown", "time": "06:21"}, {"title": "Renovação sem Demolição : T2 EP7 - <PERSON><PERSON>, <PERSON><PERSON>", "time": "07:11"}, {"title": "Reforma em Família com Karen e Mina : T5 EP9 - A Casa do Sonho de MJ", "time": "07:55"}, {"title": "Te Devo Essa! Reforma das Estrelas : T7 EP3 - O Grande Agradecimento de Regina Hall", "time": "08:43"}, {"title": "90 Dias na Cama - Felizes para Sempre? : T5 EP14 - <PERSON>?", "time": "09:32"}, {"title": "90 Dias na Cama: Antes dos 90 Dias - Com Miá Mello e <PERSON>nd<PERSON> : T7 EP10 - Guerra e Paz", "time": "10:19"}, {"title": "<PERSON><PERSON><PERSON> - Como Eles Estão Agora? : T2 EP6 - Angel e Charity", "time": "11:23"}, {"title": "<PERSON>uilos Mo<PERSON>is : T2 EP2 - A História de Olivia", "time": "12:15"}, {"title": "<PERSON>uilos Mo<PERSON>is : T2 EP2 - A História de Olivia", "time": "12:15"}, {"title": "Irmãos à Obra - Lar Sempre Lar : T6 EP1 - Transformando o <PERSON>", "time": "13:05"}, {"title": "100 Dias para Morar - Começando do Zero : T2 EP4 - Espaço Compartilhado", "time": "13:55"}, {"title": "Irmãos À Obra: Um Lar para Amar : T1 EP14 - Grandes Promessas", "time": "14:47"}, {"title": "Irmãos À Obra: Um Lar para Amar : T1 EP14 - Grandes Promessas", "time": "14:47"}, {"title": "<PERSON>ay - Irmãs pra Toda Obra : T5 EP10 - Uma Venda Complicada", "time": "15:38"}, {"title": "Christina <PERSON> : T1 EP4 - Começando do Zero", "time": "16:29"}, {"title": "<PERSON><PERSON><PERSON> - Adolescentes : T1 EP4 - Ce<PERSON>ia", "time": "17:20"}, {"title": "<PERSON><PERSON><PERSON> - Adolescentes : T1 EP4 - Ce<PERSON>ia", "time": "17:20"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "18:56"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "18:56"}]}, {"name": "Discovery Kids", "logo": "https://tvmap.com.br/images/ch/523.jpg", "programs": [{"title": "Ba Da Bean : T1 EP18 - <PERSON><PERSON>", "time": "02:27"}, {"title": "Ba Da Bean : T1 EP20 - Caos & Colagem", "time": "02:38"}, {"title": "Topo Gigio : T1 EP21 - Planta em Apuros", "time": "02:49"}, {"title": "Topo Gigio : T1 EP23 - <PERSON><PERSON> da Escola", "time": "03:00"}, {"title": "Underdogs United : T1 EP8 - A Lenda do Futebol", "time": "03:11"}, {"title": "Underdogs United : T1 EP9 - <PERSON>", "time": "03:22"}, {"title": "Kung Fu Wa : T1 EP23 - Resgate na Caverna", "time": "03:33"}, {"title": "Kung Fu Wa : T1 EP25 - Noite das Estrelas Cadentes", "time": "03:44"}, {"title": "Polly Pocket : T3 EP5 - A <PERSON>da da Praia Panda", "time": "03:55"}, {"title": "Polly Pocket : T3 EP6 - <PERSON><PERSON><PERSON> da Sala de Fuga", "time": "04:06"}, {"title": "O Show da Luna! : T3 EP4 - <PERSON> Mundo dos Cães", "time": "04:17"}, {"title": "O Show da Luna! : T3 EP18 - <PERSON><PERSON><PERSON><PERSON>", "time": "04:28"}, {"title": "Agente Binky ao Resgate! : T2 - Encrencas Falantes", "time": "05:24"}, {"title": "Agente Binky ao Resgate! : T2 - O Novo Amigo de Nola", "time": "05:36"}, {"title": "Agente Binky ao Resgate! : T2 - Cápsulas Fugitivas", "time": "05:48"}, {"title": "Mini Beat Power Rockers : T2 EP50 - O Quarto do Silêncio", "time": "06:00"}, {"title": "Mini Beat Power Rockers : T2 EP51 - Fugindo do Passado", "time": "06:05"}, {"title": "Mini Beat Power Rockers : T2 EP53 - A <PERSON><PERSON><PERSON>", "time": "06:10"}, {"title": "Mini Beat Power Rockers : T2 EP54 - Fi fa fo Fuz", "time": "06:15"}, {"title": "Mini Beat Power Rockers : T2 EP55 - <PERSON>ão Toque Esse Botão!", "time": "06:20"}, {"title": "Mini Beat Power Rockers : T2 EP56 - Tem um Inseto Aqui!", "time": "06:24"}, {"title": "Mini Beat Power Rockers : T3 EP2 - <PERSON><PERSON>, o <PERSON><PERSON>", "time": "06:28"}, {"title": "Cleo & Cuquin : T1 EP27 - <PERSON>", "time": "06:55"}, {"title": "Cleo & Cuquin : T1 EP28 - A Melhor TV do Mundo", "time": "07:02"}, {"title": "WeeBoom : T2 EP18 - Aventureiros Também <PERSON>", "time": "07:08"}, {"title": "WeeBoom : T1 EP7 - <PERSON> do Verão", "time": "07:15"}, {"title": "Irmãos Construtores e a Fábrica de Sonhos : T1 EP35 - Velozes e Curiosos", "time": "07:22"}, {"title": "O Show da Luna! : T2 EP1 - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "time": "07:57"}, {"title": "O Show da Luna! : T2 EP3 - Desenhos do Céu", "time": "08:08"}, {"title": "<PERSON><PERSON> & Baduki : T1 EP1 - O Resgate Celestial", "time": "08:19"}, {"title": "Peppa : T5 - <PERSON>; <PERSON>das; <PERSON><PERSON>; O Táxi da Dona Coelha; <PERSON><PERSON><PERSON><PERSON> de Faz de Conta", "time": "08:30"}, {"title": "Super Wish : T1 - <PERSON><PERSON>", "time": "08:55"}, {"title": "O Bicho Vai Pegar - O Chamado da Natureza : T1 - Uma Dura Lição", "time": "09:06"}, {"title": "Ba Da Bean : T1 EP5 - <PERSON> Mistério da Musa Desaparecida", "time": "09:17"}, {"title": "Ba Da Bean : T1 EP6 - <PERSON><PERSON><PERSON>lu<PERSON>", "time": "09:29"}, {"title": "Zouk a Bruxinha : T1 EP14 - A Primeira Vida do Azar", "time": "09:42"}, {"title": "Irmãos Construtores e a Fábrica de Sonhos : T1 EP20 - Caça ao Tesouro", "time": "09:53"}, {"title": "Mini Beat Power Rockers : T3 EP45 - A <PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "10:30"}, {"title": "Mini Beat Power Rockers : T3 EP46 - Com a Esquerda", "time": "10:34"}, {"title": "Hello Kitty: Super Style! : T2 EP4 - <PERSON> A<PERSON>gão", "time": "10:38"}, {"title": "Hello Kitty: Super Style! : T4 EP2 - <PERSON> Bus<PERSON> de um Sorriso", "time": "10:49"}, {"title": "Mermicorno - A Queda das Estrelas : T2 EP3 - Uma Entrega Especial", "time": "11:00"}, {"title": "Peppa Pig : <PERSON><PERSON>es; O Concurso de Abóboras; Música em Movimento; Londres; Ciências Simples", "time": "11:24"}, {"title": "O Show da Luna! : T8 EP20 - Bora pra Escola", "time": "11:47"}, {"title": "O Show da Luna! : T8 EP21 - Preto no Branco!", "time": "11:58"}, {"title": "O Show da Luna! : T8 EP22 - <PERSON>m Inventou o Barco Viking?", "time": "12:11"}, {"title": "Shasha & Milo : T1 - Nova Gata no Pedaço", "time": "12:22"}, {"title": "<PERSON>r<PERSON><PERSON> : T7 EP14 - Mi<PERSON><PERSON><PERSON> no Trem da Woohp", "time": "12:44"}, {"title": "Polly Pocket : T5 - <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "13:06"}, {"title": "Polly Pocket : <PERSON>", "time": "13:19"}, {"title": "Hello Kitty: Super Style! : T9 EP1 - <PERSON> <PERSON>", "time": "13:30"}, {"title": "Hello Kitty: Super Style! : T10 EP2 - O Bichinho do Vastus", "time": "13:41"}, {"title": "Hello Kitty: Super Style! : T8 EP1 - A Estrela do Teatro", "time": "13:52"}, {"title": "LEGO DreamZzz - As Provas dos Caçadores de Sonhos : T1 EP3 - Sonhadores Caçados", "time": "14:03"}, {"title": "Mini Beat Power Rockers : T4 EP16 - A <PERSON><PERSON>", "time": "14:27"}, {"title": "Mini Beat Power Rockers : T4 EP17 - A História de Lupi Nick", "time": "14:32"}, {"title": "Mini Beat Power Rockers : T4 EP18 - Um Dia Animado", "time": "14:37"}, {"title": "Pinóquio e Seus Amigos : T2 EP2 - O Primeiro Dia de Aula", "time": "14:41"}, {"title": "Pinóquio e Seus Amigos : T2 EP3 - O Retorno da Bruxa do Mar", "time": "14:53"}, {"title": "Peppa Pig : T5 - <PERSON><PERSON><PERSON> Páscoa; <PERSON><PERSON><PERSON>; A Polícia; O Livro da Mamãe Pig; O Zoológico", "time": "15:27"}, {"title": "Peppa Pig : T8 EP19 - Dia da Arte em Casa", "time": "15:52"}, {"title": "Peppa Pig : T8 EP21 - Brincadeiras em Dias de Sol", "time": "15:57"}, {"title": "Peppa : T5 - O Interior; Surfando; A Grande Barreira de Recife; Bumerangue; O Hospital de Bonecas", "time": "16:02"}, {"title": "<PERSON><PERSON> & Baduki : T1 EP13 - O Enigma do Tempo", "time": "16:27"}, {"title": "<PERSON><PERSON> & Baduki : T1 EP1 - O Resgate Celestial", "time": "16:38"}, {"title": "Zouk a Bruxinha : T1 EP2 - O Ogro Mestre", "time": "16:49"}, {"title": "As Múmias e o Anel Perdido", "time": "17:00"}, {"title": "As Múmias e o Anel Perdido", "time": "17:00"}, {"title": "<PERSON> - Shorts Collection : T1 EP3 - Funhouse Paintings", "time": "18:26"}, {"title": "Hello Kitty: Super Style! : T3 EP1 - A <PERSON>ulseira Encantada", "time": "18:28"}, {"title": "Hello Kitty: Super Style! : T3 EP4 - A Máquina Copiadora", "time": "18:39"}, {"title": "Mermicorno - A Queda das Estrelas : T2 EP2 - Test<PERSON> em Dupla", "time": "18:52"}, {"title": "O Show da Luna! : T7 EP14 - O Gigante dos Planetas", "time": "19:14"}, {"title": "O Show da Luna! : T7 EP2 - Plaquetas em Ação!", "time": "19:36"}, {"title": "Peppa : T8 EP10 - O Andorinhão Pequeno", "time": "19:49"}, {"title": "Peppa : T8 EP13 - <PERSON> o<PERSON> Bebês Fazem?", "time": "19:54"}, {"title": "Peppa : T8 EP12 - <PERSON><PERSON>", "time": "19:59"}]}, {"name": "Discovery Science", "logo": "https://tvmap.com.br/images/ch/472.jpg", "programs": [{"title": "Da Vinci na Prática", "time": "02:24"}, {"title": "<PERSON><PERSON>uro Visto do Espaço : T2 EP7 - A Tempestade", "time": "03:12"}, {"title": "Megaconstruções : T6 EP4 - <PERSON><PERSON>", "time": "04:00"}, {"title": "O Mais Genial : T1 EP5 - Cachoeira", "time": "04:48"}, {"title": "SIGN OFF", "time": "05:36"}, {"title": "O Mais Genial : T1 EP8 - <PERSON><PERSON><PERSON>", "time": "06:24"}, {"title": "Destiladores : T5 EP15 - Uísque Virgem", "time": "07:12"}, {"title": "<PERSON><PERSON>uro Visto do Espaço : T2 EP7 - A Tempestade", "time": "08:00"}, {"title": "Megaconstruções : T6 EP4 - <PERSON><PERSON>", "time": "08:48"}, {"title": "O Mais Genial : T1 EP5 - Cachoeira", "time": "09:36"}, {"title": "O Caçador de Múmias : T1 EP2 - A Múmia do Assassino de Lincoln", "time": "10:24"}, {"title": "Destiladores : T7 EP5 - Expresso do Pônei", "time": "11:12"}, {"title": "Destiladores : T5 EP15 - Uísque Virgem", "time": "12:00"}, {"title": "Códigos e Conspirações : T2 EP1 - Fundadores dos EUA", "time": "12:48"}, {"title": "A Fúria dos Elementos : Fogo", "time": "13:36"}, {"title": "<PERSON><PERSON> da Construção : EP3 - <PERSON> Hong Kong", "time": "14:24"}, {"title": "Projeto Greensburg : <PERSON>", "time": "15:12"}, {"title": "Desvendando o Passado : T1 EP5 - Segredos dos Assassinatos Maia", "time": "16:00"}, {"title": "Grandes Mistérios do Universo com Morgan Freeman : T3 EP9 - A Eternidade Acabará?", "time": "16:48"}, {"title": "American Titans - <PERSON>st<PERSON><PERSON><PERSON> uma Nação : T1 EP2 - <PERSON> x Scott", "time": "17:36"}, {"title": "Da Vinci na Prática", "time": "18:24"}, {"title": "Megaconstruções : T1 EP5 - Ponte sobre o Estreito de Bering", "time": "19:12"}, {"title": "Planeta Energia : T1 EP10 - Microgeração", "time": "20:00"}]}, {"name": "Discovery Theater", "logo": "https://tvmap.com.br/images/ch/430.jpg", "programs": [{"title": "<PERSON><PERSON> por Ouro : T3 EP5 - Quem É o Capitão?", "time": "02:24"}, {"title": "Alasca - A Última Fronteira : T8 EP11 - <PERSON><PERSON>", "time": "03:12"}, {"title": "Alasca - A Última Fronteira : T8 EP12 - O Natal dos Kilchers", "time": "04:00"}, {"title": "Alasca ao Extremo : T4 EP8 - Ventos da Mudança", "time": "04:48"}, {"title": "Rota Perigosa : T2 EP4 - <PERSON><PERSON><PERSON><PERSON>", "time": "05:36"}, {"title": "Ouro Selvagem : T1 EP3 - <PERSON><PERSON><PERSON><PERSON> Armados", "time": "06:24"}, {"title": "Monstros da Montanha : T7 EP3 - <PERSON><PERSON> <PERSON> Olhos Vermelhos", "time": "07:12"}, {"title": "Alasca - A Última Fronteira : T8 EP11 - <PERSON><PERSON>", "time": "08:00"}, {"title": "Alasca - A Última Fronteira : T8 EP12 - O Natal dos Kilchers", "time": "08:48"}, {"title": "Alasca ao Extremo : T4 EP8 - Ventos da Mudança", "time": "09:36"}, {"title": "Rota Perigosa : T2 EP4 - <PERSON><PERSON><PERSON><PERSON>", "time": "10:24"}, {"title": "Ouro Selvagem : T1 EP3 - <PERSON><PERSON><PERSON><PERSON> Armados", "time": "11:12"}, {"title": "Monstros da Montanha : T7 EP3 - <PERSON><PERSON> <PERSON> Olhos Vermelhos", "time": "12:00"}, {"title": "Caçadores de Pítons : T2 EP1 - Ação Emergencial", "time": "12:48"}, {"title": "Caçadores de Pítons : T2 EP2 - Torniquete de Píton", "time": "13:36"}, {"title": "Ouro Selvagem : T2 EP6 - Corrida para a Fronteira", "time": "14:24"}, {"title": "Ouro Selvagem : T2 EP6 - Corrida para a Fronteira", "time": "14:24"}, {"title": "Supercaminhões", "time": "16:00"}, {"title": "Guerreiros do Pacífico : T1 EP2 - Crise na Grande Ilha", "time": "16:48"}, {"title": "Pesca Mortal : T15 EP12 - Monstro de Nove Metros", "time": "17:36"}, {"title": "Histó<PERSON><PERSON>", "time": "18:24"}, {"title": "Baía de Bristol : T1 EP2 - A Grande Aposta", "time": "19:12"}, {"title": "Destiladores : T10 EP15 - Os Ingredientes do Outro", "time": "20:00"}]}, {"name": "Discovery Turbo", "logo": "https://tvmap.com.br/images/ch/474.jpg", "programs": [{"title": "Carros Irados : T8 EP5 - É um Desafio Novo!", "time": "01:50"}, {"title": "Trocando a Marcha com <PERSON> : T2 EP1 - A Hora do Rali", "time": "02:40"}, {"title": "<PERSON>s Reis da Sucata : T3 EP11 - Esmagando Carros & Anotando Nomes", "time": "03:30"}, {"title": "Os Reis da Sucata : T3 EP12 - <PERSON><PERSON><PERSON>, <PERSON><PERSON> e um Fusca", "time": "04:20"}, {"title": "Por Dentro da West Coast Customs : T7 EP7 - Reforma de Model-A", "time": "05:10"}, {"title": "Lendas JDM : T1 EP5 - Troca de Motores", "time": "06:00"}, {"title": "Oficina RMD : T1 EP3 - <PERSON><PERSON><PERSON><PERSON>", "time": "06:50"}, {"title": "<PERSON><PERSON> : T17 EP13 - Ford Transit", "time": "07:40"}, {"title": "<PERSON><PERSON> : T17 EP18 - Lotus Europa", "time": "08:30"}, {"title": "Oficinaço : T6 EP10 - Buick 1965 - Personalização Total", "time": "09:22"}, {"title": "Te Devo Essa! Carros das Estrelas - México", "time": "10:14"}, {"title": "<PERSON><PERSON> : T11 EP2 - Porsche 993 Targa", "time": "11:06"}, {"title": "Twin Turbos : T1 EP7 - Batalha na Arena de Touros", "time": "11:58"}, {"title": "Carros Irados : T8 EP5 - É um Desafio Novo!", "time": "12:50"}, {"title": "Trocando a Marcha com <PERSON> : T2 EP1 - A Hora do Rali", "time": "13:42"}, {"title": "<PERSON>s Reis da Sucata : T3 EP11 - Esmagando Carros & Anotando Nomes", "time": "14:34"}, {"title": "Os Reis da Sucata : T3 EP12 - <PERSON><PERSON><PERSON>, <PERSON><PERSON> e um Fusca", "time": "15:26"}, {"title": "Por Dentro da West Coast Customs : T7 EP7 - Reforma de Model-A", "time": "16:18"}, {"title": "Acumuladores de Carros : T2 EP4 - Como Agradar um Milionário", "time": "17:10"}, {"title": "Acumuladores de Carros : T2 EP4 - Como Agradar um Milionário", "time": "17:10"}, {"title": "Vida Automotiva : T1 EP7 - <PERSON> (Gol GTS)", "time": "18:02"}, {"title": "Vida Automotiva : T1 EP8 - <PERSON><PERSON> (VW Passat VR6)", "time": "18:28"}, {"title": "Viciado em Motores - Goblin Garage : T1 EP3 - Land Rover Futurista", "time": "18:54"}, {"title": "Lendas JDM : T1 EP6 - <PERSON><PERSON>", "time": "19:46"}, {"title": "Lendas JDM : T1 EP6 - <PERSON><PERSON>", "time": "19:46"}]}, {"name": "Discovery World", "logo": "https://tvmap.com.br/images/ch/431.jpg", "programs": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "02:24"}, {"title": "Vende-se Pântano : T2 EP4 - Um Lar de Um Milhão de Dólares", "time": "02:48"}, {"title": "O Hotel Ideal : EP3 - <PERSON><PERSON>", "time": "03:12"}, {"title": "O Hotel Ideal : EP5 - Costa Rica", "time": "03:36"}, {"title": "Italianos Made in India", "time": "04:00"}, {"title": "Italianos Made in China", "time": "04:48"}, {"title": "1000 Lugares para Con<PERSON>cer <PERSON> : T1 EP4 - Brasil", "time": "05:36"}, {"title": "7 Piscinas Incríveis : Refúgios para Casais", "time": "06:24"}, {"title": "Vida na Praia", "time": "06:48"}, {"title": "Se<PERSON><PERSON><PERSON> das Ilhas : T1 EP5 - Ilhas do Canal", "time": "07:12"}, {"title": "O Hotel Ideal : EP3 - <PERSON><PERSON>", "time": "08:00"}, {"title": "O Hotel Ideal : EP5 - Costa Rica", "time": "08:24"}, {"title": "Italianos Made in India", "time": "08:48"}, {"title": "Italianos Made in China", "time": "09:36"}, {"title": "1000 Lugares para Con<PERSON>cer <PERSON> : T1 EP4 - Brasil", "time": "10:24"}, {"title": "7 Piscinas Incríveis : Refúgios para Casais", "time": "11:12"}, {"title": "Vida na Praia", "time": "11:36"}, {"title": "Se<PERSON><PERSON><PERSON> das Ilhas : T1 EP5 - Ilhas do Canal", "time": "12:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON> das Ilhas : T1 EP1 - <PERSON><PERSON> Phlegrean", "time": "12:24"}, {"title": "Casa de Praia : T1 EP7 - <PERSON><PERSON>", "time": "12:48"}, {"title": "Casa de Praia : T1 EP8 - Praia Radical", "time": "13:12"}, {"title": "Paraísos pelo Mundo - Praias : T1 EP14 - Um Oásis na Tailândia", "time": "13:36"}, {"title": "Paraísos pelo Mundo - Praias : T1 EP2 - A Beleza Natural dos Açores", "time": "14:00"}, {"title": "Lugares Secretos para Nadar : T3 EP9 - Praia Secreta no Havaí", "time": "14:24"}, {"title": "Lugares Secretos para Nadar : T2 EP5 - Cingapura", "time": "15:12"}, {"title": "O Hotel Ideal : EP2 - Londres", "time": "16:00"}, {"title": "1000 Lugares para <PERSON><PERSON>cer <PERSON> : T1 EP1 - Alasca", "time": "17:12"}, {"title": "<PERSON><PERSON><PERSON> da América: Bacon : T1 EP9 - <PERSON><PERSON><PERSON><PERSON> por <PERSON>", "time": "18:00"}, {"title": "<PERSON><PERSON><PERSON> da América: Bacon : T1 EP10 - <PERSON>acon", "time": "18:24"}, {"title": "<PERSON><PERSON><PERSON> da América: Bacon : T1 EP12 - Paraíso do Bacon", "time": "18:48"}, {"title": "O Poder dos Alimentos : EP8 - Dietas Radicais", "time": "19:12"}, {"title": "O Poder dos Alimentos : EP9 - Segurança Alimentar", "time": "19:36"}]}, {"name": "E!", "logo": "https://tvmap.com.br/images/ch/555.jpg", "programs": [{"title": "Family Karma : T2 EP9 - A Última Chance", "time": "02:00"}, {"title": "Family Karma : T3 EP1 - Meu Grande Casamento Indiano", "time": "03:00"}, {"title": "Project Runway : T16 EP10 - <PERSON><PERSON><PERSON>", "time": "04:00"}, {"title": "House of Villains : T1 EP8 - <PERSON><PERSON><PERSON><PERSON> Entram em um Bar...", "time": "05:00"}, {"title": "Family Karma : T3 EP2 - Aproveitando ao Máximo", "time": "06:00"}, {"title": "Family Karma : T3 EP3 - <PERSON> <PERSON>", "time": "06:50"}, {"title": "Vanderpump Rules : T2 EP5 - <PERSON><PERSON> Sexy e Problemas de Saúde", "time": "07:40"}, {"title": "Vanderpump Rules : T2 EP6 - Romance para Todo Lado", "time": "08:30"}, {"title": "House of Kardashian : T1 EP2 - <PERSON><PERSON>, Mentiras e Videotape", "time": "09:20"}, {"title": "House of Kardashian : T1 EP3 - <PERSON> Novo Poder", "time": "10:15"}, {"title": "Werk It", "time": "12:10"}, {"title": "Werk It", "time": "12:10"}, {"title": "The Kardashians: Billion Dollar Dynasty", "time": "14:00"}, {"title": "Vanderpump Rules : T2 EP5 - <PERSON><PERSON> Sexy e Problemas de Saúde", "time": "15:45"}, {"title": "Vanderpump Rules : T2 EP6 - Romance para Todo Lado", "time": "16:35"}, {"title": "Million Dollar Listing Los Angeles : T11 EP10 - Pelado no Quintal", "time": "17:25"}, {"title": "Million Dollar Listing Los Angeles : T11 EP10 - Pelado no Quintal", "time": "17:25"}, {"title": "Million Dollar Listing Los Angeles : T11 EP11 - Estilo Dubai", "time": "18:15"}, {"title": "Project Runway : T16 EP11 - Guerreiras do Câncer de Mama", "time": "19:30"}]}, {"name": "ESPN", "logo": "https://tvmap.com.br/images/ch/480.jpg", "programs": [{"title": "Remo x Novorizontino - Campeonato Brasileiro - Série B : Remo x Novorizontino", "time": "02:00"}, {"title": "Mundo F", "time": "04:00"}, {"title": "Atlético Bucaramanga x Atlético-MG - Copa Sul-Americana : Atlético Bucaramanga x Atlético-MG", "time": "05:00"}, {"title": "Linha de Passe: Mesa-Redonda", "time": "07:00"}, {"title": "SportsCenter", "time": "08:00"}, {"title": "ESPN F360", "time": "09:00"}, {"title": "ESPN F360", "time": "09:00"}, {"title": "SportsCenter", "time": "11:00"}, {"title": "F Show", "time": "13:00"}, {"title": "Flamengo x Bayer Leverkusen - Amistoso de Futebol Masculino : Flamengo x Bayer Leverkusen", "time": "14:25"}, {"title": "Flamengo x Bayer Leverkusen - Amistoso de Futebol Masculino : Flamengo x Bayer Leverkusen", "time": "14:25"}, {"title": "Mundo F", "time": "16:30"}, {"title": "Equipe F", "time": "17:30"}, {"title": "Atlético-GO x Criciúma - Campeonato Brasileiro - Série B : Atlético-GO x Criciúma", "time": "18:50"}, {"title": "Atlético-GO x Criciúma - Campeonato Brasileiro - Série B : Atlético-GO x Criciúma", "time": "18:50"}]}, {"name": "Film & Arts", "logo": "https://tvmap.com.br/images/ch/608.jpg", "programs": [{"title": "<PERSON><PERSON> - <PERSON>", "time": "02:10"}, {"title": "<PERSON> & the Duke : T4 EP5 - <PERSON>", "time": "03:00"}, {"title": "<PERSON> & the Duke : T4 EP6 - O Fugitivo", "time": "04:00"}, {"title": "Showtrial : T1 EP3 - <PERSON>se", "time": "05:00"}, {"title": "Film&Arts Desde o West End : EP2 - Les Miserables", "time": "06:00"}, {"title": "O <PERSON> de <PERSON>", "time": "06:30"}, {"title": "Gauthier Dance: <PERSON><PERSON> <PERSON>", "time": "07:30"}, {"title": "<PERSON><PERSON> - <PERSON>", "time": "09:10"}, {"title": "Lang <PERSON> & Gina Alice Interpretam Saint-Saëns", "time": "10:05"}, {"title": "<PERSON> & the Duke : T4 EP5 - <PERSON>", "time": "12:00"}, {"title": "<PERSON> & the Duke : T4 EP6 - O Fugitivo", "time": "13:00"}, {"title": "Showtrial : T1 EP3 - <PERSON>se", "time": "14:00"}, {"title": "O Primeiro que Disse", "time": "15:00"}, {"title": "O <PERSON> de <PERSON>", "time": "17:05"}, {"title": "O <PERSON> de <PERSON>", "time": "17:05"}, {"title": "Royal Swedish Opera: <PERSON><PERSON> - Melancholia", "time": "18:05"}, {"title": "Royal Swedish Opera: <PERSON><PERSON> - Melancholia", "time": "18:05"}]}, {"name": "FishTV", "logo": "https://tvmap.com.br/images/ch/609.jpg", "programs": [{"title": "A Meta : T7 EP9 - Travessia de Caiaque nas Lagoas do RS", "time": "02:00"}, {"title": "Destinos : T11 EP14 - <PERSON><PERSON><PERSON>, <PERSON><PERSON> e Mar - Uma Expedição Inesquecível por Curaçao", "time": "03:00"}, {"title": "Slow Fishing : T5 EP9 - Piracanjuba em Destaque na Pescaria de Porto Rico", "time": "03:30"}, {"title": "Alta Performance : T2 EP5 - <PERSON><PERSON><PERSON> até o Fim no Torneio Individual STA Fishing", "time": "04:00"}, {"title": "Vida na Água : T2 EP4 - Pescaria Emocionante com Recorde Brasileiro Feminino para Lo Mourão", "time": "05:30"}, {"title": "SIGN OFF", "time": "05:40"}, {"title": "<PERSON><PERSON><PERSON><PERSON> : T7 EP5 - Os Grandes Black Bass de Orlando!", "time": "07:00"}, {"title": "Além da Margem - <PERSON><PERSON> o Último <PERSON>", "time": "08:00"}, {"title": "Alma de Gigante : T1 EP7 - <PERSON><PERSON><PERSON><PERSON><PERSON>?", "time": "08:30"}, {"title": "Slow Fishing : T5 EP9 - Piracanjuba em Destaque na Pescaria de Porto Rico", "time": "09:00"}, {"title": "Destinos : T11 EP14 - <PERSON><PERSON><PERSON>, <PERSON><PERSON> e Mar - Uma Expedição Inesquecível por Curaçao", "time": "09:30"}, {"title": "A Meta : T7 EP9 - Travessia de Caiaque nas Lagoas do RS", "time": "10:00"}, {"title": "Provas e Torneios : T9 EP11 - 1ª Edição do Rainhas do Araguaia", "time": "11:00"}, {"title": "Remos e Rumos : T7 EP15 - Expedição no Rio Bararati em Roraima: com Dublê e Triplê de Trairão", "time": "11:30"}, {"title": "5° Tempo CBP : T4 EP10 - Os Sistemas de Pesca que Fazem a Diferença nos Pesqueiros", "time": "12:00"}, {"title": "Planeta Turismo : T4 EP13 - Etapa Luarca: La Caridad - Ribadeo (Galícias) do Caminho de Santiago de Compostela", "time": "13:00"}, {"title": "Pesca Goiás : T1 EP2 - Disputa, Adrenalina e Turismo - A Pesca Tomou Conta de Quirinópolis-GO", "time": "13:30"}, {"title": "Fish TV News : T7 EP24 - 2º Workshop de Pesca Esportiva no Amapá e Entrega do PNP", "time": "14:00"}, {"title": "Pesqueiros do Brasil : T6 EP18 - Muitos Tambas no Beijinho no Pesqueiro Quinta do Aveiro", "time": "14:30"}, {"title": "TV Shopping Brasil", "time": "15:00"}, {"title": "TV Shopping Brasil", "time": "15:00"}, {"title": "Elas na Pesca : T6 EP8 - Pescaria no Joice Tur <PERSON>", "time": "19:00"}, {"title": "Biopesca : T10 EP7 - Piraputanga do Rio Miranda - Hotel Cabana do Pescador", "time": "19:30"}]}, {"name": "Food Network", "logo": "https://tvmap.com.br/images/ch/672.jpg", "programs": [{"title": "Sabor na Brasa : T3 EP7 - Lama e Aço no Novo México", "time": "02:18"}, {"title": "Sabor na Brasa : T3 EP8 - Carne e Vinho na Califórnia", "time": "02:41"}, {"title": "Lanchonetes Clássicas com Guy Fieri : T23 EP8 - Com o Garfo e com a Mão", "time": "03:04"}, {"title": "Lanchonetes Clássicas com Guy <PERSON> : T23 EP9 - <PERSON><PERSON><PERSON>", "time": "03:25"}, {"title": "Lanchonetes Clássicas com Guy <PERSON> : T23 EP10 - <PERSON> Gostinho Britânico", "time": "03:46"}, {"title": "Lanchonetes Clássicas com Guy <PERSON> : T23 EP11 - <PERSON><PERSON><PERSON> da Dieta", "time": "04:09"}, {"title": "A Guerra dos Cupcakes : T4 EP5 - Final da Copa", "time": "05:14"}, {"title": "<PERSON><PERSON> com Bobby Flay : T5 EP3 - Novata e Sous-Chef", "time": "06:00"}, {"title": "<PERSON><PERSON> com Bobby Flay : T5 EP4 - Tex-Mex Contra Sudeste Asiá<PERSON>o", "time": "06:23"}, {"title": "Chopped - Júnior : T4 EP9 - Festa do Sanduíche", "time": "06:46"}, {"title": "Food Truck - A Grande Corrida : T5 EP2 - Cachorro-Quentíssimo em Tucson", "time": "07:32"}, {"title": "Lanchonetes Clássicas com Guy <PERSON>eri : T23 EP13 - Italo-Caribenha", "time": "08:18"}, {"title": "Lanchonetes Clássicas com Guy <PERSON>eri : T23 EP15 - Banquetes de Leste a Oeste", "time": "08:41"}, {"title": "Lanchonetes Clássicas com Guy <PERSON> : T24 EP1 - <PERSON><PERSON> as <PERSON><PERSON>", "time": "09:04"}, {"title": "Lanchonetes Clássicas com Guy Fieri : T24 EP2 - <PERSON><PERSON> de Família", "time": "09:25"}, {"title": "Na Rota do Sabor : T7 EP2 - Paraíso Noturno", "time": "09:46"}, {"title": "Na Rota do Sabor : T7 EP2 - Paraíso Noturno", "time": "09:46"}, {"title": "Loucos por Churrasco : T1 EP5 - Não Brinque com o Churrasco do Texas", "time": "10:32"}, {"title": "Loucos por Churrasco : T1 EP8 - Churrasco Extremo da Nova Inglaterra", "time": "10:53"}, {"title": "Sabores da América com <PERSON> : T4 EP9 - <PERSON><PERSON>, por <PERSON><PERSON>or", "time": "11:14"}, {"title": "Sabores da América com <PERSON> : T4 EP10 - Defumados e Picantes", "time": "11:37"}, {"title": "Sabor na Brasa : T3 EP10 - <PERSON><PERSON><PERSON>", "time": "12:00"}, {"title": "Sabor na Brasa : T3 EP11 - <PERSON><PERSON><PERSON> de Lareira", "time": "12:23"}, {"title": "A Guerra dos Bolos : T3 EP12 - Onde Está Wally?", "time": "12:46"}, {"title": "A Guerra dos Cupcakes : T4 EP5 - Final da Copa", "time": "13:28"}, {"title": "Kitchen Boss : T2 EP7 - <PERSON>s", "time": "14:14"}, {"title": "Kitchen Boss : T2 EP34 - Aniversário de Roma", "time": "14:37"}, {"title": "Cake Boss : T7 EP10 - <PERSON><PERSON> e Surpresa", "time": "15:23"}, {"title": "<PERSON> vs. <PERSON> - <PERSON> : T1 EP4 - Guerra de Casamento", "time": "15:46"}, {"title": "A Disputa dos Confeiteiros - Primavera : T6 EP2 - Céus de Primavera", "time": "16:32"}, {"title": "Pequenos Confeiteiros : T5 EP3 - Sorvetes Diferentes", "time": "17:18"}, {"title": "Pequenos Confeiteiros : T5 EP3 - Sorvetes Diferentes", "time": "17:18"}, {"title": "Pequenos Confeiteiros : T5 EP4 - Sobremesas de Lasanha", "time": "18:04"}, {"title": "A Guerra dos Bolos : T4 EP1 - As Meninas Superpoderosas", "time": "18:46"}, {"title": "A Guerra dos Cupcakes : T4 EP4 - Wicked", "time": "19:32"}]}, {"name": "Fox News", "logo": "https://tvmap.com.br/images/ch/466.jpg", "programs": [{"title": "<PERSON> Primetime", "time": "02:00"}, {"title": "Hannity", "time": "03:00"}, {"title": "Gut<PERSON>!", "time": "04:00"}, {"title": "The Ingraham Angle", "time": "05:00"}, {"title": "FOX & Friends First", "time": "06:00"}, {"title": "FOX and Friends", "time": "07:00"}, {"title": "America's Newsroom", "time": "10:00"}, {"title": "The Faulkner Focus", "time": "12:00"}, {"title": "Outnumbered", "time": "13:00"}, {"title": "America Reports", "time": "14:00"}, {"title": "The Story With <PERSON>", "time": "16:00"}, {"title": "The Will Cain Show", "time": "17:00"}, {"title": "The Five", "time": "18:00"}, {"title": "Special Report With <PERSON><PERSON>", "time": "19:00"}, {"title": "The Ingraham Angle", "time": "20:00"}]}, {"name": "Fox Sports 2", "logo": "https://tvmap.com.br/images/ch/650.jpg", "programs": [{"title": "Quartas de Final, Jogo 2: Ourinhos Basquete x São José - Liga de Basquete Feminino : Quartas de Final, Jogo 2: Ourinhos Basquete x São José", "time": "02:00"}, {"title": "San Antonio Spurs x Charlotte Hornets - NBA Summer League : San Antonio Spurs x Charlotte Hornets", "time": "04:00"}, {"title": "San Antonio Spurs x Charlotte Hornets - NBA Summer League : San Antonio Spurs x Charlotte Hornets", "time": "04:00"}, {"title": "Argentinos Juniors x Boca Juniors - Campeonato Argentino : Argentinos Juniors x Boca Juniors", "time": "06:00"}, {"title": "Fórmula Indy - GP de Iowa - Corrida 2 : GP de Iowa - Corrida 2", "time": "08:00"}, {"title": "Operário-PR x CRB - Campeonato Brasileiro - Série B : Operário-PR x CRB", "time": "10:30"}, {"title": "Resenha ESPN : <PERSON><PERSON><PERSON>", "time": "13:00"}, {"title": "SportsCenter", "time": "13:30"}, {"title": "<PERSON><PERSON> da Vez : <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> da Juventus", "time": "15:30"}, {"title": "Remo x Novorizontino - Campeonato Brasileiro - Série B : Remo x Novorizontino", "time": "16:30"}, {"title": "Remo x Novorizontino - Campeonato Brasileiro - Série B : Remo x Novorizontino", "time": "16:30"}, {"title": "Indy NXT Series - GP de Iowa : GP de Iowa", "time": "18:30"}, {"title": "Greenland : T5 EP2 - Thailand", "time": "19:30"}, {"title": "Tour de France 2025", "time": "20:00"}]}, {"name": "Gazeta", "logo": "https://tvmap.com.br/images/ch/402.jpg", "programs": [{"title": "Gazeta Shopping", "time": "02:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "06:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "06:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "06:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "13:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "14:30"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "14:30"}, {"title": "Gazeta Esportiva", "time": "18:00"}, {"title": "Jornal da Gazeta", "time": "19:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "20:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "20:00"}]}, {"name": "Globo", "logo": "https://tvmap.com.br/images/ch/399.jpg", "programs": [{"title": "<PERSON><PERSON>", "time": "01:50"}, {"title": "Comédia Na Madruga - Vai Que Cola", "time": "02:35"}, {"title": "<PERSON><PERSON>:", "time": "04:00"}, {"title": "<PERSON><PERSON>:", "time": "04:00"}, {"title": "Bom Dia São Paulo", "time": "06:00"}, {"title": "Bom Dia São Paulo", "time": "06:00"}, {"title": "Bom Dia Brasil", "time": "08:30"}, {"title": "Encontro com Patrícia Poeta", "time": "09:30"}, {"title": "Encontro com Patrícia Poeta", "time": "09:30"}, {"title": "<PERSON><PERSON>", "time": "10:35"}, {"title": "SPTV - 1ª Edição", "time": "11:45"}, {"title": "Globo Esporte", "time": "13:00"}, {"title": "Jornal Ho<PERSON>", "time": "13:25"}, {"title": "Edição Especial - História de Amor", "time": "14:45"}, {"title": "<PERSON><PERSON><PERSON>rde - : Indiana Jones e o Reino da Caveira de Cristal", "time": "15:25"}, {"title": "Boletim SP2:", "time": "17:05"}, {"title": "Vale a Pena Ver de Novo - : A Viagem", "time": "17:15"}, {"title": "Vale a Pena Ver de Novo - : A Viagem", "time": "17:15"}, {"title": "Êta Mundo Melhor!", "time": "18:25"}, {"title": "SP2:", "time": "19:10"}, {"title": "<PERSON><PERSON>", "time": "19:40"}, {"title": "Jornal Nacional", "time": "20:30"}, {"title": "Vale Tudo", "time": "21:20"}, {"title": "Globo Repórter", "time": "22:25"}, {"title": "Globo Repórter", "time": "22:25"}, {"title": "Sessão Globoplay - : Rico<PERSON>", "time": "23:15"}, {"title": "Jornal da Globo", "time": "00:00"}, {"title": "Conversa com Bial", "time": "00:50"}]}, {"name": "HBO Family", "logo": "https://tvmap.com.br/images/ch/497.jpg", "programs": [{"title": "<PERSON> e o Enigma do Príncipe", "time": "00:00"}, {"title": "A Casa do Lago", "time": "02:36"}, {"title": "Meu Malvado Favorito 3", "time": "04:28"}, {"title": "Meu Malvado Favorito 3", "time": "04:28"}, {"title": "Angry Birds - O Filme", "time": "06:00"}, {"title": "<PERSON><PERSON><PERSON> - <PERSON>", "time": "07:39"}, {"title": "<PERSON><PERSON>", "time": "09:47"}, {"title": "<PERSON><PERSON>", "time": "09:47"}, {"title": "Os Smurfs 2", "time": "11:32"}, {"title": "Os Smurfs 2", "time": "11:32"}, {"title": "Harold e o Lápis Mágico", "time": "13:19"}, {"title": "Garfield Fora de Casa", "time": "14:51"}, {"title": "Garfield Fora de Casa", "time": "14:51"}, {"title": "Goosebumps - Monstros e Arrepios", "time": "16:34"}, {"title": "Goosebumps - Monstros e Arrepios", "time": "16:34"}, {"title": "Angry Birds - O Filme", "time": "18:20"}, {"title": "Garfield Fora de Casa", "time": "20:00"}]}, {"name": "HBO HD", "logo": "https://tvmap.com.br/images/ch/446.jpg", "programs": [{"title": "Saturday Night - A Noite que Mudou a Comédia", "time": "00:55"}, {"title": "1992", "time": "02:47"}, {"title": "No Olho do Tornado", "time": "04:27"}, {"title": "No Olho do Tornado", "time": "04:27"}, {"title": "<PERSON>", "time": "06:00"}, {"title": "<PERSON>", "time": "06:00"}, {"title": "Quero Matar Meu Chefe 2", "time": "08:45"}, {"title": "Quero Matar Meu Chefe 2", "time": "08:45"}, {"title": "Um Filme Minecraft", "time": "10:35"}, {"title": "Adão Negro", "time": "12:20"}, {"title": "Adão Negro", "time": "12:20"}, {"title": "Shazam! Fúria dos Deuses", "time": "14:30"}, {"title": "Shazam! Fúria dos Deuses", "time": "14:30"}, {"title": "<PERSON> e as <PERSON><PERSON><PERSON><PERSON><PERSON> da Morte - Parte 1", "time": "16:45"}, {"title": "<PERSON> e as <PERSON><PERSON><PERSON><PERSON><PERSON> da Morte - Parte 1", "time": "16:45"}, {"title": "Os Fantasmas Ain<PERSON> se Divertem - Beetlejuice Beetlejuice", "time": "19:15"}, {"title": "Os Fantasmas Ain<PERSON> se Divertem - Beetlejuice Beetlejuice", "time": "19:15"}]}, {"name": "HBO Mundi", "logo": "https://tvmap.com.br/images/ch/502.jpg", "programs": [{"title": "Não se Preocupe Querida", "time": "00:40"}, {"title": "Rio Congelado", "time": "02:50"}, {"title": "La Posada", "time": "04:25"}, {"title": "La Posada", "time": "04:25"}, {"title": "Camponeses", "time": "06:00"}, {"title": "Syriana - A Indústria do Petróleo", "time": "08:00"}, {"title": "<PERSON><PERSON>", "time": "10:10"}, {"title": "Advogado do Diabo", "time": "11:55"}, {"title": "Advogado do Diabo", "time": "11:55"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "14:25"}, {"title": "Família <PERSON>no : T3 EP12 - <PERSON>", "time": "15:30"}, {"title": "Família Soprano : T3 EP13 - <PERSON>é<PERSON><PERSON> de um Homem Só", "time": "16:35"}, {"title": "Família Soprano : T4 EP1 - Dívidas Públicas e Particulares", "time": "17:40"}, {"title": "Coisa de Menino : T1 EP3 - Casca Grossa", "time": "18:40"}, {"title": "Spotlight - <PERSON><PERSON><PERSON><PERSON>", "time": "19:45"}, {"title": "Spotlight - <PERSON><PERSON><PERSON><PERSON>", "time": "19:45"}]}, {"name": "HBO Plus", "logo": "https://tvmap.com.br/images/ch/495.jpg", "programs": [{"title": "O Senhor dos Anéis - A Guerra de Rohirrim", "time": "01:50"}, {"title": "Sombras da Noite", "time": "04:05"}, {"title": "Sombras da Noite", "time": "04:05"}, {"title": "Sob o Domínio do Medo", "time": "06:00"}, {"title": "Resident Evil 3 - A Extinção", "time": "07:55"}, {"title": "A Morte do Demônio - A Ascensão", "time": "09:35"}, {"title": "A Morte do Demônio - A Ascensão", "time": "09:35"}, {"title": "Batman Vs. Superman - <PERSON>", "time": "11:20"}, {"title": "Batman Vs. Superman - <PERSON>", "time": "11:20"}, {"title": "Os Horrores do Caddo Lake", "time": "13:55"}, {"title": "Os Horrores do Caddo Lake", "time": "13:55"}, {"title": "Batman - <PERSON>leiro das Trevas Ressurge", "time": "15:45"}, {"title": "Batman - <PERSON>leiro das Trevas Ressurge", "time": "15:45"}, {"title": "Acompanhante Perfeita", "time": "18:30"}]}, {"name": "HBO Pop", "logo": "https://tvmap.com.br/images/ch/665.jpg", "programs": [{"title": "<PERSON><PERSON><PERSON>a Vagina", "time": "01:30"}, {"title": "A Torcida", "time": "03:20"}, {"title": "<PERSON> - <PERSON><PERSON><PERSON><PERSON> que Você Ama", "time": "04:55"}, {"title": "Como Matar Seu <PERSON>", "time": "06:00"}, {"title": "O Cara", "time": "07:30"}, {"title": "Minhas Adoráveis Ex-Namoradas", "time": "08:55"}, {"title": "Minhas Adoráveis Ex-Namoradas", "time": "08:55"}, {"title": "É Assim que Acaba", "time": "10:40"}, {"title": "Uma Festa de Arromba", "time": "12:55"}, {"title": "<PERSON>ro Matar <PERSON>", "time": "14:40"}, {"title": "<PERSON>ro Matar <PERSON>", "time": "14:40"}, {"title": "Cúmplice em Fuga", "time": "16:20"}, {"title": "Cúmplice em Fuga", "time": "16:20"}, {"title": "A Verdade Nua e Crua", "time": "18:05"}, {"title": "É Assim que Acaba", "time": "19:45"}, {"title": "É Assim que Acaba", "time": "19:45"}]}, {"name": "HBO Signature", "logo": "https://tvmap.com.br/images/ch/501.jpg", "programs": [{"title": "King no <PERSON>o", "time": "02:00"}, {"title": "<PERSON><PERSON> <PERSON>", "time": "04:00"}, {"title": "<PERSON><PERSON> <PERSON>", "time": "04:00"}, {"title": "<PERSON> - <PERSON> <PERSON>", "time": "06:00"}, {"title": "<PERSON> - <PERSON> <PERSON>", "time": "06:00"}, {"title": "Gia - Fama e Destruição", "time": "08:05"}, {"title": "Game of Thrones : T3 EP6 - The Climb", "time": "10:10"}, {"title": "Game of Thrones : T3 EP7 - <PERSON> Bear and the Maiden Fair", "time": "11:10"}, {"title": "Game of Thrones : T3 EP8 - Second Sons", "time": "12:10"}, {"title": "Game of Thrones : T3 EP8 - Second Sons", "time": "12:10"}, {"title": "Game of Thrones : T3 EP9 - The Rains of Castamere", "time": "13:10"}, {"title": "Game of Thrones : T3 EP10 - <PERSON><PERSON><PERSON>", "time": "14:05"}, {"title": "Você Não Conhece o Jack", "time": "15:10"}, {"title": "Dear Ms.: A Revolution in Print", "time": "17:30"}, {"title": "Game of Thrones : T3 EP6 - The Climb", "time": "19:00"}, {"title": "Game of Thrones : T3 EP7 - <PERSON> Bear and the Maiden Fair", "time": "20:00"}]}, {"name": "HBO2", "logo": "https://tvmap.com.br/images/ch/496.jpg", "programs": [{"title": "Megatubarão 2", "time": "01:55"}, {"title": "<PERSON> Exorcismo de <PERSON>", "time": "03:55"}, {"title": "<PERSON> Exorcismo de <PERSON>", "time": "03:55"}, {"title": "Anjos e Demônios", "time": "06:00"}, {"title": "Anjos e Demônios", "time": "06:00"}, {"title": "The Alto Knights - Máfia e Poder", "time": "08:20"}, {"title": "Furiosa - <PERSON><PERSON>", "time": "10:25"}, {"title": "1992", "time": "13:00"}, {"title": "A Idade Dourada : T3 EP4 - O Casamento É uma Aposta", "time": "14:40"}, {"title": "A Idade Dourada : T3 EP4 - O Casamento É uma Aposta", "time": "14:40"}, {"title": "Mickey 17", "time": "15:40"}, {"title": "Mickey 17", "time": "15:40"}, {"title": "<PERSON> - A Lenda do Skate : T1 EP3 - <PERSON> Cabeça pra Baixo", "time": "18:05"}, {"title": "HBO Movies", "time": "19:00"}]}, {"name": "Lifetime", "logo": "https://tvmap.com.br/images/ch/664.jpg", "programs": [{"title": "Terror no Paraíso", "time": "01:20"}, {"title": "Perfeita Obsessão", "time": "02:55"}, {"title": "O Assassino da Casa Inteligente", "time": "04:30"}, {"title": "Dance Moms : T9 EP11 - Competição Épica: <PERSON> <PERSON><PERSON><PERSON> o que <PERSON>", "time": "06:00"}, {"title": "Amor à Primeira Mentira", "time": "06:25"}, {"title": "Amor à Primeira Mentira", "time": "06:25"}, {"title": "A Vizinha Suspeita", "time": "08:05"}, {"title": "Perfeita Obsessão", "time": "09:45"}, {"title": "Perfeita Obsessão", "time": "09:45"}, {"title": "A Octomãe", "time": "11:20"}, {"title": "<PERSON><PERSON><PERSON>", "time": "13:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "14:40"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "14:40"}, {"title": "De Olho em Você", "time": "16:20"}, {"title": "The Real Estate Brasil", "time": "18:00"}, {"title": "Amores que Enganam : T2 EP10 - Amor Compartilhado", "time": "19:00"}]}, {"name": "MTV", "logo": "https://tvmap.com.br/images/ch/534.jpg", "programs": [{"title": "Just Tattoo Of Us : T3 EP4 - <PERSON> Pior <PERSON> do Mundo", "time": "01:45"}, {"title": "Ridiculousness : T34 EP16 - <PERSON> e <PERSON>", "time": "02:35"}, {"title": "Ridiculousness : T34 EP17 - <PERSON> e <PERSON>XI", "time": "02:55"}, {"title": "M is for Music", "time": "03:20"}, {"title": "M is for Music", "time": "03:20"}, {"title": "M is for Music", "time": "06:00"}, {"title": "MTV Top 20", "time": "06:45"}, {"title": "Jovens e Mães - Novo Capítulo : T1 EP4 - <PERSON><PERSON>, Sua Escolha", "time": "08:00"}, {"title": "Jovens e Mães - Novo Capítulo : T1 EP5 - <PERSON><PERSON><PERSON>as Escolhas", "time": "08:40"}, {"title": "Jovens e Mães - Novo Capítulo : T1 EP6 - <PERSON><PERSON>s e Mães Irmãs para Sempre", "time": "09:25"}, {"title": "Catfish : T7 EP11 - Kiaira e Cortney", "time": "10:15"}, {"title": "Catfish : T7 EP12 - <PERSON> & <PERSON>", "time": "10:55"}, {"title": "De Férias com o Ex: Caribe - VIP", "time": "11:40"}, {"title": "MTV Hits", "time": "13:15"}, {"title": "Ridiculousness : T28 EP22 - <PERSON><PERSON> e Sterling DLXXIV", "time": "14:15"}, {"title": "Ridiculousness : T28 EP39 - <PERSON><PERSON> e Sterling DLXXVII", "time": "14:40"}, {"title": "Catfish : T2 EP11 - <PERSON><PERSON><PERSON> <PERSON>", "time": "15:25"}, {"title": "Catfish : T5 EP1 - <PERSON><PERSON>, <PERSON> & <PERSON>", "time": "16:10"}, {"title": "Catfish : T4 EP12 - <PERSON><PERSON> <PERSON>", "time": "16:55"}, {"title": "Catfish : T4 EP13 - <PERSON><PERSON><PERSON> <PERSON>", "time": "17:35"}, {"title": "Catfish : T2 EP3 - <PERSON>", "time": "18:25"}, {"title": "Namoro no Sigilo : T2 EP6 - <PERSON><PERSON> & Jose", "time": "19:15"}, {"title": "Pego no Pulo - Infiel : T3 EP21 - Amor no Clube de Comédia", "time": "20:00"}]}, {"name": "Music Box Brazil", "logo": "https://tvmap.com.br/images/ch/597.jpg", "programs": [{"title": "Music Drops : Avant Premiere Sr - <PERSON> 2", "time": "02:23"}, {"title": "Performances do Music : T1 EP11 - <PERSON><PERSON><PERSON><PERSON> <PERSON> - <PERSON>", "time": "02:34"}, {"title": "Cortes do Music : T1 EP80 - <PERSON><PERSON>e Instante em Diante - Itamar <PERSON>", "time": "02:35"}, {"title": "Music Drops : Backstage - Porão do Rock - Plebe Rude", "time": "02:37"}, {"title": "João Rock 2018 : Franscis<PERSON>, el Hombre", "time": "02:43"}, {"title": "Cortes do Music : T1 EP108 - Música Mov 2021 - <PERSON>", "time": "02:44"}, {"title": "Music Drops : <PERSON><PERSON><PERSON>", "time": "02:45"}, {"title": "Especial: Music 10 Anos - Rio Grande do Sul : <PERSON><PERSON>", "time": "02:47"}, {"title": "Cortes do Music : T1 EP59 - FNM 2014 - <PERSON>", "time": "02:48"}, {"title": "Music Drops : <PERSON><PERSON><PERSON>", "time": "02:50"}, {"title": "Performances do Music : T1 EP20 - T<PERSON> em Casa - Onze e 20 - <PERSON>ra Você", "time": "02:57"}, {"title": "Cortes do Music : T1 EP82 - Vozes Negras Importam 2", "time": "02:59"}, {"title": "Music Drops : Prime Rock 2023 - <PERSON>", "time": "03:00"}, {"title": "Pixote Part. Raça Negra: Insegurança", "time": "03:02"}, {"title": "Cortes do Music : T1 EP57 - <PERSON> 2018 - <PERSON><PERSON><PERSON>", "time": "03:03"}, {"title": "Music Drops : The Mönic", "time": "03:04"}, {"title": "Performances do Music : T1 EP74 - Black Alien e <PERSON><PERSON>", "time": "03:06"}, {"title": "Cortes do Music : T1 EP73 - Prime Rock Curitiba - Capital Inicial", "time": "03:08"}, {"title": "Music Drops : Backstage - Porão Do Rock - CPM 22", "time": "03:09"}, {"title": "Performances do Music : T1 EP144 - <PERSON><PERSON> - O Gato Gateia e Você Quer Saber", "time": "03:12"}, {"title": "Cortes do Music : T1 EP102 - Música Mov 2021 - Arismar do Espírito Santo", "time": "03:13"}, {"title": "Music Drops : Prime Rock 2023 - <PERSON>", "time": "03:14"}, {"title": "João Rock 2018 : Fr<PERSON>", "time": "03:17"}, {"title": "Cortes do Music : T1 EP99 - Music Box 10 <PERSON>os - <PERSON>", "time": "03:18"}, {"title": "Music Drops : Micareta SP - Gloria Groove", "time": "03:19"}, {"title": "Performances do Music : T1 EP170 - <PERSON> - <PERSON>", "time": "03:21"}, {"title": "Cortes do Music : T1 EP62 - FNM 2014 - <PERSON>", "time": "03:22"}, {"title": "Music Drops : Prêmio Music Drops", "time": "03:23"}, {"title": "Performances do Music : T1 EP165 - <PERSON><PERSON> - <PERSON><PERSON> o Fim", "time": "03:28"}, {"title": "Cortes do Music : T1 EP88 - <PERSON><PERSON><PERSON> - Crypta - Backstage - Porão do Rock", "time": "03:29"}, {"title": "João Rock 2018 : Natiruts", "time": "03:32"}, {"title": "Cortes do Music : T1 EP88 - Project 46 - Backstage - Porão do Rock", "time": "03:33"}, {"title": "Jam Sessions : T1 EP49 - <PERSON><PERSON> - <PERSON>", "time": "03:34"}, {"title": "Music Drops : Backstage - Porão do Rock - Selvagens à Procura de Lei", "time": "03:38"}, {"title": "Music Drops : Prime Rock Curitiba - Legião Urbana", "time": "03:40"}, {"title": "Cortes do Music : T1 EP71 - Prime Rock Curitiba - Paralamas do Sucesso", "time": "03:42"}, {"title": "Music Drops : Rock Fun Fest 2", "time": "03:43"}, {"title": "Performances do Music : T1 EP153 - <PERSON>us Amigos Estão Velhos - <PERSON><PERSON>", "time": "03:49"}, {"title": "Cortes do Music : T1 EP78 - Hip Hop Brazil - <PERSON><PERSON>", "time": "03:51"}, {"title": "Music Drops : Festival RPB 2023 - Biquíni", "time": "03:52"}, {"title": "Cortes do Music : T1 EP96 - Music Box 10 Anos - Sunflower Jam", "time": "03:54"}, {"title": "Jam Sessions : T1 EP18 - <PERSON>", "time": "03:55"}, {"title": "Music Drops : <PERSON><PERSON>", "time": "03:58"}, {"title": "Performances do Music : T1 EP173 - <PERSON><PERSON> - <PERSON><PERSON>", "time": "03:59"}, {"title": "Cortes do Music : T1 EP103 - Música Mov 2021 - <PERSON><PERSON>", "time": "04:00"}, {"title": "Music Drops : <PERSON><PERSON>", "time": "04:01"}, {"title": "Performances do Music : T1 EP162 - <PERSON><PERSON><PERSON> e Ravel - Cedoide", "time": "04:03"}, {"title": "Cortes do Music : T1 EP100 - Music Box 10 Anos - Fulanos e Ciclanos", "time": "04:04"}, {"title": "Performances do Music : T1 EP4 - <PERSON><PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> - Vital e Sua Moto", "time": "04:08"}, {"title": "Cortes do Music : T1 EP90 - Badauí - CPM 22 - Backstage - Porão do Rock", "time": "04:09"}, {"title": "Music Drops : Prime Rock 2023 - <PERSON>", "time": "04:10"}, {"title": "Performances do Music : T1 EP1 - Carnamusic - André<PERSON><PERSON> e <PERSON><PERSON>, Pensamento Verde", "time": "04:15"}, {"title": "Cortes do Music : T1 EP89 - Raimundos - Backstage - Porão do Rock", "time": "04:16"}, {"title": "Music Drops : <PERSON><PERSON> da Língua 2", "time": "04:18"}, {"title": "Performances do Music : T1 EP91 - FNM 2014: <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Lembrar de Mim", "time": "04:23"}, {"title": "Cortes do Music : T1 EP86 - Documentário - Gritando Hc!", "time": "04:24"}, {"title": "Music Drops : Zimbra", "time": "04:25"}, {"title": "Performances do Music : T1 EP197 - Porão do Rock 2023 - Dr<PERSON>na - Game", "time": "04:28"}, {"title": "Cortes do Music : T1 EP58 - FNM 2014: Ana <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "time": "04:29"}, {"title": "Babilônia", "time": "05:11"}, {"title": "DoSol Sessions : T1 EP31 - Orquestra Greiosa; <PERSON><PERSON><PERSON><PERSON>", "time": "06:30"}, {"title": "<PERSON><PERSON><PERSON> na Galeria : T1 EP3 - <PERSON>", "time": "07:00"}, {"title": "O So<PERSON> que Você <PERSON>", "time": "08:00"}, {"title": "Music Drops : <PERSON>", "time": "08:15"}, {"title": "Buscando Buskers : <PERSON><PERSON>", "time": "08:30"}, {"title": "Rap Box : T1 EP22 - Cronistas de Rua", "time": "09:00"}, {"title": "Rap Box : T1 EP23 - The Gust MCs", "time": "09:25"}, {"title": "Music Drops : <PERSON><PERSON>", "time": "09:50"}, {"title": "Highway - Manual Prático de Sobrevivência na Estrada", "time": "10:30"}, {"title": "Jukebox", "time": "11:00"}, {"title": "Acorda, Amor : T1 EP3 - Onde o Brasil Aprendeu a Liberdade", "time": "12:00"}, {"title": "Sonar : Maratona Sonar com Kyria", "time": "13:00"}, {"title": "Papo com Cachaça : T1 EP5 - Machete Bomb", "time": "14:00"}, {"title": "Babilônia", "time": "14:35"}, {"title": "Festival Curau : <PERSON><PERSON> e Tracie", "time": "15:00"}, {"title": "MV Bill", "time": "15:55"}, {"title": "Donato em Relatos de Amor", "time": "16:25"}, {"title": "Hip Hop Brazil", "time": "16:55"}, {"title": "Music Drops : <PERSON><PERSON>", "time": "18:20"}, {"title": "<PERSON><PERSON>", "time": "18:30"}, {"title": "Estúdio Showlivre : Baby", "time": "19:00"}, {"title": "Babilônia", "time": "19:30"}]}, {"name": "NHK World Premium", "logo": "https://tvmap.com.br/images/ch/567.jpg", "programs": [{"title": "News Around Japan", "time": "01:05"}, {"title": "World Weather", "time": "02:50"}, {"title": "World Music Album", "time": "02:55"}, {"title": "News & Weather", "time": "03:00"}, {"title": "Chinese Navi", "time": "03:10"}, {"title": "Dear Japan", "time": "03:30"}, {"title": "Grand Sumo 2025 July Tournament at IG Arena", "time": "03:55"}, {"title": "Grand Sumo 2025 July Tournament at IG Arena", "time": "03:55"}, {"title": "News", "time": "06:00"}, {"title": "Blue & Yellow 10min", "time": "06:10"}, {"title": "Peek-a-boo", "time": "06:20"}, {"title": "With Mother", "time": "06:35"}, {"title": "NHK News 7", "time": "07:00"}, {"title": "Trivia Quizzes - You'll Get Scolded by <PERSON><PERSON><PERSON><PERSON>!", "time": "07:57"}, {"title": "World Weather", "time": "08:43"}, {"title": "NHK News 845", "time": "08:45"}, {"title": "News Watch 9", "time": "09:00"}, {"title": "Somewhere Street", "time": "10:30"}, {"title": "International News Report 2025", "time": "11:30"}, {"title": "News & Weather", "time": "11:40"}, {"title": "World Weather", "time": "12:29"}, {"title": "Sinbei - A Samurai with a Child", "time": "12:30"}, {"title": "Mini Program", "time": "13:13"}, {"title": "My Gardening", "time": "13:15"}, {"title": "Go Focus", "time": "13:40"}, {"title": "NHK Academia", "time": "14:10"}, {"title": "World Weather", "time": "14:40"}, {"title": "Kid's Discovery", "time": "14:45"}, {"title": "<PERSON><PERSON>", "time": "15:30"}, {"title": "I Would Rather Die Alone", "time": "15:45"}, {"title": "Mini Program", "time": "16:30"}, {"title": "Delicious Japan", "time": "16:35"}, {"title": "World Weather", "time": "16:58"}, {"title": "Grand Sumo 2025 July Tournament Highlights", "time": "17:00"}, {"title": "NHK Regional Showcases", "time": "17:25"}, {"title": "News: Good Morning, Japan", "time": "18:00"}, {"title": "World Weather", "time": "19:59"}]}, {"name": "<PERSON>.", "logo": "https://tvmap.com.br/images/ch/610.jpg", "programs": [{"title": "As Aventura<PERSON> de <PERSON> : T1 EP15 - <PERSON><PERSON> e o Ano Novo Chinês; <PERSON><PERSON> e os Ingressos Desaparecidos", "time": "02:25"}, {"title": "As Aventuras de <PERSON> : T1 EP16 - <PERSON><PERSON> e os Jogos de Verão; <PERSON><PERSON> Vai Acampar", "time": "02:45"}, {"title": "<PERSON> Shark, o Grande Show! : T3 EP12 - <PERSON><PERSON><PERSON> do Blizz; <PERSON><PERSON> da Amiga", "time": "03:10"}, {"title": "<PERSON> Shark, o Grande Show! : T3 EP13 - <PERSON> Favorito do Papai; <PERSON> Padrão Gold<PERSON>", "time": "03:35"}, {"title": "<PERSON> Shark, o Grande Show! : T3 EP14 - Disputa de Gentilezas; A Peixe Fada dos Dentes Substituta", "time": "04:00"}, {"title": "<PERSON> and the Monster Machines : T8 - Problema com os Pneus", "time": "04:20"}, {"title": "<PERSON> and the Monster Machines : T8 - <PERSON>, <PERSON> e <PERSON>", "time": "05:10"}, {"title": "Blaze e os Monster Machines : T8 - A Primeira Corrida do Blaze", "time": "05:35"}, {"title": "Patrulha Canina : T6 EP7 - <PERSON>s Filhotes Salvam o Violão do Gustavo; <PERSON><PERSON> <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "time": "06:00"}, {"title": "Patrulha Canina : T6 EP8 - <PERSON>s Filhotes Sal<PERSON> a Hora da Soneca; <PERSON>s Filhotes Salvam o Ovo da Galinheta", "time": "06:25"}, {"title": "Patrulha Canina : T6 EP10 - <PERSON>s Filhotes Salvam um Peixe-Boi; <PERSON>s Filhotes Salvam o Café da Manhã", "time": "06:50"}, {"title": "R<PERSON><PERSON> e Sua Turma : T1 EP2 - A Turma Constrói uma Bicicletaria!; A Turma Constrói uma Superbanheira!", "time": "07:15"}, {"title": "R<PERSON><PERSON> e Sua Turma : T1 EP3 - A Turma Constrói uma Sorveteria!; A Turma Conserta um Barulho!", "time": "07:40"}, {"title": "Barbapapa e Família : Seguindo o Fluxo; O Decodificador", "time": "08:05"}, {"title": "DORA : T2 - Mapa do Tesouro do Uncle Treasure; O Grande Mistério da Chave", "time": "08:30"}, {"title": "Tiny Chef : T2 EP4 - <PERSON><PERSON>; <PERSON><PERSON>", "time": "08:55"}, {"title": "Deer Squad : T1 EP18 - <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "09:20"}, {"title": "Deer Squad : T3 EP11 - <PERSON><PERSON>; Uma Noite na Ilha Brilhante", "time": "09:45"}, {"title": "DORA : T3 EP8 - Cantando na Banheira; Mostre e Compartilhe", "time": "10:10"}, {"title": "Barbapapa e Família : T1 - O Tablete; Bigodes Admiráveis", "time": "10:35"}, {"title": "Barbapapa e Família : Que a Justiça Seja Feita; A Grande Limpeza da Primavera", "time": "10:55"}, {"title": "Tainá e os Guardiões da Amazônia : T2 EP8 - <PERSON> da Toca", "time": "11:20"}, {"title": "Música de Brinquedo : T2 EP4 - Rock and Roll <PERSON>", "time": "11:35"}, {"title": "Os Chocolix : T4 EP7 - Os Presentes", "time": "11:50"}, {"title": "Os Chocolix : T4 EP9 - O Craque", "time": "12:00"}, {"title": "Os Chocolix : T4 EP10 - <PERSON>sfera da Tempestade", "time": "12:05"}, {"title": "DORA : T3 EP10 - Party do Luar", "time": "12:15"}, {"title": "DORA : T3 EP9 - A Surpresa de Aniversário da Dora", "time": "12:25"}, {"title": "DORA : T2 EP11 - O Concurso de Culinária das Grandmas; Mapa Sumiu", "time": "12:40"}, {"title": "Patrul<PERSON> : T10 EP15 - <PERSON>s Filhotes Salvam uma Rena Filhote!; <PERSON>s Filhotes Sal<PERSON> e Seu Sósia!", "time": "13:05"}, {"title": "R<PERSON><PERSON> e Sua Turma : T2 EP22 - A Turma Constrói um Hotel!; A Turma Constrói uma Trilha!", "time": "13:30"}, {"title": "<PERSON><PERSON><PERSON> e Sua Turma : T2 - A Turma Constrói uma Placa para a Sierra Sparkle!", "time": "13:55"}, {"title": "<PERSON> Shark, o Grande Show! : T3 EP3 - <PERSON><PERSON><PERSON><PERSON><PERSON> da Goldie; <PERSON><PERSON><PERSON>", "time": "14:10"}, {"title": "<PERSON> Shark, o Grande Show! : T2 - A Redenção do Cãolesma", "time": "14:30"}, {"title": "Barbapapa e Família : Para Onde Foi, Barbabravo?; Galinha Gigante do Mal", "time": "14:45"}, {"title": "Barbapapa e Família : T1 - Para Mamãe; Barbamontoados", "time": "15:10"}, {"title": "R<PERSON>ble e Sua Turma : T2 EP17 - A Turma Comemora o Dia de Ação de Graças!; A Turma Constrói um Brinquedo de Foguete!", "time": "15:35"}, {"title": "<PERSON><PERSON><PERSON> e Sua Turma : T2 - A Turma Constrói um Ônibus com Energia Solar!", "time": "15:55"}, {"title": "R<PERSON><PERSON> e Sua Turma : T2 EP18 - A Turma Ergue uma Pista de Pouso!; A Turma Constrói um Skate Gigante!", "time": "16:10"}, {"title": "Deer Squad : T2 EP3 - <PERSON><PERSON><PERSON> em Fuga; Luzes, Câmera, Ação", "time": "16:35"}, {"title": "Deer Squad : T2 EP4 - <PERSON><PERSON>; A Grande Festa", "time": "17:05"}, {"title": "Patrulha Canina : T3 EP18 - Festival da Geleia; Os Filhotes Salvam um Porco no Windsurfe", "time": "17:50"}, {"title": "Rubble e Sua Turma : T1 EP17 - A Turma Constrói uma Biblioteca Loja de Chá!; A Turma Constrói uma Pista de Dança!", "time": "18:15"}, {"title": "DORA : T2 - <PERSON> Botas; A Mochila Fechada", "time": "18:45"}, {"title": "Tiny Chef : T2 EP3 - <PERSON><PERSON>; <PERSON><PERSON> Arc<PERSON>-<PERSON><PERSON>", "time": "19:05"}, {"title": "Barbapapa e Família : T1 - Os Barbapapais das Cavernas; Os Pequenos Monstros", "time": "19:30"}, {"title": "Barbapapa e Família : <PERSON>; <PERSON><PERSON>", "time": "19:55"}]}, {"name": "Polish<PERSON>", "logo": "https://tvmap.com.br/images/ch/642.jpg", "programs": [{"title": "Programa Pago", "time": "02:00"}, {"title": "Programa Pago", "time": "02:00"}, {"title": "Programa Pago", "time": "06:00"}, {"title": "Programa Pago", "time": "06:00"}, {"title": "Programa Pago", "time": "10:00"}, {"title": "Programa Pago", "time": "10:00"}, {"title": "Programa Pago", "time": "14:00"}, {"title": "Programa Pago", "time": "14:00"}, {"title": "Programa Pago", "time": "18:00"}, {"title": "Programa Pago", "time": "18:00"}]}, {"name": "Prime Box Brazil", "logo": "https://tvmap.com.br/images/ch/611.jpg", "programs": [{"title": "Brasil 2050 : T1 EP12 - Resíduos", "time": "02:30"}, {"title": "Time de Fábrica", "time": "03:00"}, {"title": "O Menino das Estrelas", "time": "04:18"}, {"title": "Nas Pegadas do Sertão", "time": "05:00"}, {"title": "Enquanto nos Encarávamos", "time": "05:38"}, {"title": "30 Dias - Um Carnaval Entre a Alegria e a Desilusão", "time": "06:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "07:16"}, {"title": "Z", "time": "07:21"}, {"title": "<PERSON><PERSON> - Transtornos da Mente", "time": "08:00"}, {"title": "A Sombra Interior", "time": "08:42"}, {"title": "Partiu?! : T1 EP4 - <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "time": "09:00"}, {"title": "Trago a Pessoa Amada", "time": "09:30"}, {"title": "Memórias do Brasil : T2 EP3 - <PERSON><PERSON> Bara", "time": "10:00"}, {"title": "Sbørnia em Revista", "time": "11:00"}, {"title": "Listando : T1 EP4 - Filmes Sobre Games", "time": "11:30"}, {"title": "Listando : T1 EP10 - Comédias Não Costumam V<PERSON> o <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>", "time": "11:45"}, {"title": "Fair Play?", "time": "12:00"}, {"title": "Fair Play?", "time": "12:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "13:30"}, {"title": "Noite <PERSON>", "time": "13:52"}, {"title": "Antes do Livro Didático, o Cocar", "time": "14:00"}, {"title": "Trago a Pessoa Amada", "time": "14:30"}, {"title": "1001 Noites", "time": "15:00"}, {"title": "1001 Noites", "time": "15:00"}, {"title": "Movie Quotes : T1 EP2 - O Menino e a Garça e Simplesmente 30", "time": "19:00"}, {"title": "Movie Quotes : T1 EP3 - <PERSON> e <PERSON>", "time": "19:15"}, {"title": "Brasil 2050 : Eficiência Energética", "time": "19:30"}]}, {"name": "RAI", "logo": "https://tvmap.com.br/images/ch/563.jpg", "programs": [{"title": "Tg1 Mattina Estate", "time": "01:45"}, {"title": "UnoMattina Estate", "time": "03:30"}, {"title": "UnoMattina Estate", "time": "03:30"}, {"title": "Camper in viaggio", "time": "06:30"}, {"title": "Camper", "time": "07:00"}, {"title": "Tg1", "time": "08:30"}, {"title": "Tg Sport", "time": "09:00"}, {"title": "Quante storie", "time": "09:15"}, {"title": "Agorà Estate", "time": "09:45"}, {"title": "Agorà Estate", "time": "09:45"}, {"title": "Rubrica TG2", "time": "11:45"}, {"title": "Tg1", "time": "12:00"}, {"title": "Tg1", "time": "12:00"}, {"title": "Reazione a catena", "time": "13:45"}, {"title": "Tg Sport", "time": "15:30"}, {"title": "Cartoni animati", "time": "16:00"}, {"title": "Via dei Matti n. 0", "time": "16:30"}, {"title": "<PERSON><PERSON>", "time": "17:00"}, {"title": "Geo", "time": "17:45"}, {"title": "Il paradiso delle signore", "time": "18:30"}, {"title": "A<PERSON>ari tuoi", "time": "19:15"}]}, {"name": "Record News", "logo": "https://tvmap.com.br/images/ch/530.jpg", "programs": [{"title": "Balanço Geral", "time": "01:40"}, {"title": "Hora News", "time": "05:30"}, {"title": "Record News Rural", "time": "05:45"}, {"title": "Nosso Tempo", "time": "06:30"}, {"title": "Aldeia News", "time": "07:00"}, {"title": "Inovação & Negócios", "time": "07:10"}, {"title": "Record News Rural", "time": "08:00"}, {"title": "Alerta Brasil", "time": "08:45"}, {"title": "Hora News", "time": "10:30"}, {"title": "Conexão Record News", "time": "13:30"}, {"title": "Hora News", "time": "15:30"}, {"title": "Link Record News", "time": "18:00"}, {"title": "Esporte Record News", "time": "18:45"}, {"title": "News 19 Horas", "time": "19:00"}, {"title": "Elas com a Bola", "time": "19:45"}]}, {"name": "Rede Brasil", "logo": "https://tvmap.com.br/images/ch/629.jpg", "programs": [{"title": "Poder<PERSON>", "time": "02:30"}, {"title": "Shazam!", "time": "03:00"}, {"title": "Speed Racer", "time": "04:00"}, {"title": "Brasil News", "time": "05:30"}, {"title": "Igreja Unida Deus Proverá", "time": "06:00"}, {"title": "Séries Douradas", "time": "08:00"}, {"title": "<PERSON><PERSON>, a Patroa e as Crian<PERSON>s", "time": "08:50"}, {"title": "Punky, a Levada da Breca", "time": "09:15"}, {"title": "Programa Pago", "time": "09:45"}, {"title": "<PERSON><PERSON> em <PERSON>", "time": "10:30"}, {"title": "Brasil News", "time": "12:00"}, {"title": "Séries Douradas", "time": "13:00"}, {"title": "Voltz Man", "time": "13:45"}, {"title": "Viagem ao Fundo do Mar", "time": "14:00"}, {"title": "Ateliê na TV", "time": "15:30"}, {"title": "Oração da Noite", "time": "16:45"}, {"title": "<PERSON><PERSON>, a Patroa e as Crian<PERSON>s", "time": "17:15"}, {"title": "Agente 86", "time": "18:00"}, {"title": "Brasil News", "time": "18:30"}, {"title": "O Túnel do Tempo", "time": "19:30"}]}, {"name": "Rede Família", "logo": "https://tvmap.com.br/images/ch/581.jpg", "programs": [{"title": "Programação IURD", "time": "00:00"}, {"title": "Programação IURD", "time": "05:00"}, {"title": "Programa Pago", "time": "08:00"}, {"title": "Família Kids", "time": "09:30"}, {"title": "Família Kids", "time": "09:30"}, {"title": "Tá no Ar Campinas", "time": "11:00"}, {"title": "Ligados no Esporte", "time": "12:00"}, {"title": "Programa Pago", "time": "13:00"}, {"title": "<PERSON><PERSON>", "time": "15:30"}, {"title": "<PERSON>", "time": "16:30"}, {"title": "Alerta Total", "time": "17:00"}, {"title": "Alerta Total", "time": "17:00"}, {"title": "RF News", "time": "19:00"}]}, {"name": "Rede Vida", "logo": "https://tvmap.com.br/images/ch/460.jpg", "programs": [{"title": "Sant<PERSON><PERSON><PERSON> da Vida : <PERSON><PERSON>", "time": "02:15"}, {"title": "Terço da Misericórdia", "time": "03:10"}, {"title": "Novena Maria Passa na Frente", "time": "03:15"}, {"title": "Terço Bizantino", "time": "03:45"}, {"title": "Santo Rosário com Frei Gilson", "time": "04:00"}, {"title": "Santo Rosário com Frei Gilson", "time": "04:00"}, {"title": "Santo Terço dos Filhos do Pai Eterno", "time": "06:00"}, {"title": "O Pão Nosso", "time": "06:25"}, {"title": "Encontro com Cristo", "time": "06:30"}, {"title": "Santuário Basílica do Divino Pai Eterno : Missa", "time": "06:55"}, {"title": "Novena Maria Passa na Frente", "time": "08:00"}, {"title": "Crônicas de Fé e Afeto", "time": "08:25"}, {"title": "Novena do Perpétuo Socorro", "time": "08:30"}, {"title": "Missa de Nossa Senhora <PERSON>", "time": "09:00"}, {"title": "Novena dos Filhos e Filhas de São José", "time": "10:30"}, {"title": "Escolhas da Vida", "time": "11:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON> Vida", "time": "12:00"}, {"title": "Vida Melhor", "time": "12:30"}, {"title": "Juntos com Nossa Senhora de Fátima", "time": "14:30"}, {"title": "<PERSON><PERSON>", "time": "15:10"}, {"title": "Escolhas da Vida", "time": "16:15"}, {"title": "Novena dos Filhos e Filhas de São José", "time": "17:00"}, {"title": "O Santo Terço", "time": "18:00"}, {"title": "JCTV", "time": "18:30"}, {"title": "Jornal da Vida : <PERSON><PERSON> da Esperança", "time": "18:55"}, {"title": "Sant<PERSON><PERSON><PERSON> da Vida : <PERSON><PERSON>", "time": "19:00"}, {"title": "Crônicas de Fé e Afeto", "time": "19:55"}]}, {"name": "RedeTV!", "logo": "https://tvmap.com.br/images/ch/401.jpg", "programs": [{"title": "Mad<PERSON><PERSON>", "time": "02:00"}, {"title": "Igreja da Graça no Seu Lar", "time": "03:00"}, {"title": "Igreja Internacional da Graça de Deus", "time": "05:00"}, {"title": "Igreja Internacional da Graça de Deus", "time": "05:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "08:30"}, {"title": "<PERSON><PERSON><PERSON>", "time": "09:30"}, {"title": "Fica com a Gente", "time": "10:00"}, {"title": "Bola na Rede", "time": "11:30"}, {"title": "Igreja Universal do Reino de Deus", "time": "13:00"}, {"title": "A Tarde É Sua", "time": "15:00"}, {"title": "Igreja Universal do Reino de Deus", "time": "17:00"}, {"title": "Brasil do Povo", "time": "18:00"}, {"title": "RedeTV! News", "time": "19:55"}]}, {"name": "Sony", "logo": "https://tvmap.com.br/images/ch/508.jpg", "programs": [{"title": "Rede de Corrupção", "time": "01:19"}, {"title": "Everybody Hates Chris : T4 EP15 - Everybody Hates Boxing", "time": "03:10"}, {"title": "Everybody Hates Chris : T4 EP16 - Everybody Hates Lasagna", "time": "03:40"}, {"title": "Mary Kills People : T2 EP5 - Come to Jesus", "time": "04:10"}, {"title": "Saving Hope : T4 EP5 - Heart of Stone", "time": "05:10"}, {"title": "Betty's Call : T9 EP25 - Fehleinschätzungen", "time": "06:00"}, {"title": "<PERSON>'s Call : T9 EP26 - <PERSON><PERSON>om<PERSON>, <PERSON><PERSON> g<PERSON>t", "time": "06:50"}, {"title": "Everybody Hates Chris : T4 EP17 - Everybody Hates Spring Break", "time": "07:40"}, {"title": "Everybody Hates Chris : T4 EP18 - Everybody Hates the Car", "time": "08:10"}, {"title": "Everybody Hates Chris : T4 EP19 - Everybody Hates Back Talk", "time": "08:40"}, {"title": "Everybody Hates Chris : T4 EP20 - Everybody Hates <PERSON><PERSON>", "time": "09:10"}, {"title": "Designing Women : T7 EP7 - <PERSON><PERSON> Rush In", "time": "09:35"}, {"title": "Designing Women : T7 EP8 - Love Letters", "time": "10:05"}, {"title": "Designing Women : T7 EP9 - The Vision Thing", "time": "10:35"}, {"title": "The Nanny : T1 EP5 - Here Comes the Brood", "time": "11:05"}, {"title": "The Nanny : T1 EP9 - Personal Business", "time": "11:35"}, {"title": "The Nanny : T1 EP6 - <PERSON> <PERSON>, the Husband, the Wife and Her Mother", "time": "12:05"}, {"title": "Shrek para Sempre", "time": "12:35"}, {"title": "The Nanny : T6 EP1 - The Honeymoon's Overboard", "time": "14:10"}, {"title": "The Nanny : T6 EP2 - <PERSON><PERSON>", "time": "14:40"}, {"title": "The Nanny : T6 EP3 - Once a Secretary, Always a Secretary", "time": "15:10"}, {"title": "The Nanny : T6 EP4 - <PERSON>'s Parents", "time": "15:40"}, {"title": "The Good Doctor : T5 EP2 - Piece of Cake", "time": "16:10"}, {"title": "The Good Doctor : T5 EP3 - Measure of Intelligence", "time": "17:05"}, {"title": "Minions 2 - A Origem de Gru", "time": "18:00"}, {"title": "Shrek para Sempre", "time": "19:35"}, {"title": "Shrek para Sempre", "time": "19:35"}]}, {"name": "Space", "logo": "https://tvmap.com.br/images/ch/511.jpg", "programs": [{"title": "CSI: Miami : T8 EP5 - Semente do Mal", "time": "02:06"}, {"title": "CSI Investigação Criminal : T11 EP3 - <PERSON><PERSON>", "time": "02:50"}, {"title": "CSI Investigação Criminal : T11 EP14 - All That Cremains", "time": "03:35"}, {"title": "CSI: Miami : T8 EP6 - <PERSON>, <PERSON><PERSON><PERSON>?", "time": "04:20"}, {"title": "CSI: Miami", "time": "05:04"}, {"title": "Movie Talk", "time": "05:49"}, {"title": "<PERSON><PERSON><PERSON>", "time": "06:00"}, {"title": "CSI: Miami : T3 EP12 - <PERSON><PERSON><PERSON><PERSON>", "time": "06:19"}, {"title": "Uma Noite de Crime - Anarquia", "time": "07:04"}, {"title": "Uma Noite de Crime - Anarquia", "time": "07:04"}, {"title": "12 Horas para Sobreviver - O Ano da Eleição", "time": "08:54"}, {"title": "12 Horas para Sobreviver - O Ano da Eleição", "time": "08:54"}, {"title": "Oblivion", "time": "10:45"}, {"title": "No Limite do Amanhã", "time": "12:54"}, {"title": "CSI : T7 EP12 - <PERSON>", "time": "14:51"}, {"title": "CSI : T7 EP12 - <PERSON>", "time": "14:51"}, {"title": "CSI : T7 EP14 - Meet Market", "time": "15:38"}, {"title": "CSI Investigação Criminal : T7 EP15 - <PERSON><PERSON> da Gravidade", "time": "16:26"}, {"title": "CSI: Miami : T3 EP13 - Assassino de Policiais", "time": "17:12"}, {"title": "CSI: Miami : T3 EP14 - <PERSON><PERSON><PERSON> uma No<PERSON>", "time": "18:00"}, {"title": "CSI Investigação Criminal : T7 EP16 - Monstro na Caixa", "time": "18:47"}, {"title": "CSI Investigação Criminal : T7 EP8 - Coincidência", "time": "19:32"}]}, {"name": "TBS", "logo": "https://tvmap.com.br/images/ch/519.jpg", "programs": [{"title": "Os Dez Mandamentos", "time": "02:19"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON>", "time": "03:13"}, {"title": "Bahar", "time": "04:10"}, {"title": "O Canto do Pássaro", "time": "04:53"}, {"title": "Matches : T2 EP6 - Maldições Gratiluz", "time": "05:37"}, {"title": "Matches : T2 EP7 - Focados", "time": "06:00"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON>", "time": "06:22"}, {"title": "Rosario Tijeras", "time": "07:20"}, {"title": "Rosario Tijeras", "time": "07:20"}, {"title": "Esque<PERSON>-me se <PERSON>r", "time": "08:05"}, {"title": "Bahar", "time": "09:36"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON>", "time": "10:22"}, {"title": "Os Dez Mandamentos", "time": "11:19"}, {"title": "<PERSON><PERSON>", "time": "12:12"}, {"title": "Rosario Tijeras", "time": "13:42"}, {"title": "A Promessa", "time": "14:29"}, {"title": "O Canto do Pássaro", "time": "15:26"}, {"title": "Esque<PERSON>-me se <PERSON>r", "time": "16:59"}, {"title": "Bahar", "time": "18:31"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON>", "time": "19:20"}]}, {"name": "Telecine Action", "logo": "https://tvmap.com.br/images/ch/489.jpg", "programs": [{"title": "Instinto Predador", "time": "01:35"}, {"title": "Transformers - A Era da Extinção", "time": "03:20"}, {"title": "Transformers - A Era da Extinção", "time": "03:20"}, {"title": "Agente das Sombras", "time": "06:05"}, {"title": "O Atirador - Legado", "time": "07:55"}, {"title": "O Escorpião Rei", "time": "09:40"}, {"title": "O Escorpião Rei", "time": "09:40"}, {"title": "MIB: Homens de Preto - Internacional", "time": "11:15"}, {"title": "MIB: Homens de Preto - Internacional", "time": "11:15"}, {"title": "<PERSON><PERSON><PERSON>", "time": "13:10"}, {"title": "57 Segundos", "time": "15:05"}, {"title": "<PERSON> - <PERSON>", "time": "16:50"}, {"title": "<PERSON> - <PERSON>", "time": "16:50"}, {"title": "Distrito 666", "time": "18:50"}]}, {"name": "Telecine <PERSON>", "logo": "https://tvmap.com.br/images/ch/492.jpg", "programs": [{"title": "<PERSON><PERSON><PERSON>", "time": "01:40"}, {"title": "<PERSON> - <PERSON><PERSON> Gostamos de Gente", "time": "04:25"}, {"title": "<PERSON> - <PERSON><PERSON> Gostamos de Gente", "time": "04:25"}, {"title": "<PERSON><PERSON>", "time": "06:00"}, {"title": "<PERSON><PERSON>", "time": "06:00"}, {"title": "As Praias <PERSON> Agnès", "time": "08:05"}, {"title": "<PERSON><PERSON> da Ribalta", "time": "10:00"}, {"title": "O Show de Truman - O Show da Vida", "time": "12:25"}, {"title": "O Show de Truman - O Show da Vida", "time": "12:25"}, {"title": "Houria - Dançando no Silêncio", "time": "14:10"}, {"title": "Houria - Dançando no Silêncio", "time": "14:10"}, {"title": "Vidas Passadas", "time": "15:55"}, {"title": "No Coração do Mundo", "time": "17:45"}, {"title": "Monstro", "time": "19:50"}, {"title": "Monstro", "time": "19:50"}]}, {"name": "Telecine Fun", "logo": "https://tvmap.com.br/images/ch/487.jpg", "programs": [{"title": "Loucas pra Casar", "time": "01:35"}, {"title": "Dois + Dois", "time": "03:30"}, {"title": "Nunca Fui Santa", "time": "05:25"}, {"title": "Lucicreide V<PERSON>", "time": "06:55"}, {"title": "Lucicreide V<PERSON>", "time": "06:55"}, {"title": "Princesa por Acidente", "time": "08:35"}, {"title": "<PERSON><PERSON><PERSON><PERSON>, E<PERSON>el<PERSON>", "time": "10:10"}, {"title": "Fala Sério, Mãe!", "time": "12:00"}, {"title": "Fala Sério, Mãe!", "time": "12:00"}, {"title": "Bee Movie - A História de uma Abelha", "time": "13:30"}, {"title": "O Auto da Compadecida", "time": "15:10"}, {"title": "Sapatinho Vermelho e os Sete Anões", "time": "17:00"}, {"title": "Sapatinho Vermelho e os Sete Anões", "time": "17:00"}, {"title": "Tô Ryca! 2", "time": "18:40"}]}, {"name": "Telecine <PERSON>", "logo": "https://tvmap.com.br/images/ch/491.jpg", "programs": [{"title": "Códigos Ocultos", "time": "01:30"}, {"title": "A Protetora", "time": "03:25"}, {"title": "Terror na Água", "time": "05:10"}, {"title": "Vai que Cola - O Filme", "time": "06:45"}, {"title": "Vai que Cola - O Filme", "time": "06:45"}, {"title": "Policial em Apuros", "time": "08:30"}, {"title": "<PERSON><PERSON>", "time": "10:15"}, {"title": "À Procura da Felicidade", "time": "12:05"}, {"title": "À Procura da Felicidade", "time": "12:05"}, {"title": "<PERSON> - <PERSON>", "time": "14:05"}, {"title": "<PERSON> - <PERSON>", "time": "14:05"}, {"title": "Rambo - Programado para Matar", "time": "16:20"}, {"title": "Riddick 3", "time": "18:00"}]}, {"name": "Telecine Premium", "logo": "https://tvmap.com.br/images/ch/488.jpg", "programs": [{"title": "Cães de Guerra", "time": "01:45"}, {"title": "<PERSON><PERSON>", "time": "03:30"}, {"title": "<PERSON><PERSON>", "time": "03:30"}, {"title": "<PERSON>", "time": "05:35"}, {"title": "Perfekta: Uma Aventura da Escola de Gênios", "time": "07:30"}, {"title": "Terapia do <PERSON>", "time": "09:15"}, {"title": "Terapia do <PERSON>", "time": "09:15"}, {"title": "Apex", "time": "11:10"}, {"title": "Morando com o Crush", "time": "12:55"}, {"title": "<PERSON><PERSON><PERSON> da Máscara Dourada", "time": "14:35"}, {"title": "<PERSON><PERSON><PERSON> da Máscara Dourada", "time": "14:35"}, {"title": "Missão Impossível - Efeito Fallout", "time": "16:05"}, {"title": "Missão Impossível - Efeito Fallout", "time": "16:05"}, {"title": "A Menina e o Dragão", "time": "18:35"}]}, {"name": "Telecine Touch", "logo": "https://tvmap.com.br/images/ch/490.jpg", "programs": [{"title": "Derrapada", "time": "02:20"}, {"title": "Milagre na Cela 7", "time": "04:15"}, {"title": "Milagre na Cela 7", "time": "04:15"}, {"title": "The Box - No Ritmo do Coração", "time": "06:35"}, {"title": "The Box - No Ritmo do Coração", "time": "06:35"}, {"title": "Eu Não Quero Voltar Sozinho", "time": "08:20"}, {"title": "Um Porto Seguro", "time": "08:45"}, {"title": "Um Porto Seguro", "time": "08:45"}, {"title": "<PERSON><PERSON><PERSON><PERSON>", "time": "10:50"}, {"title": "A Filha do Presidente", "time": "12:35"}, {"title": "Belas e Perseguidas", "time": "14:30"}, {"title": "Belas e Perseguidas", "time": "14:30"}, {"title": "First Love - <PERSON><PERSON><PERSON><PERSON> o Amor", "time": "16:10"}, {"title": "12 Anos de Escravidão", "time": "17:55"}]}, {"name": "Terra Viva", "logo": "https://tvmap.com.br/images/ch/613.jpg", "programs": [{"title": "Dia Dia Rural", "time": "02:30"}, {"title": "AgroBand", "time": "04:00"}, {"title": "Que Seja Eterno Enquanto Cure", "time": "05:30"}, {"title": "Terraviva DBO na TV", "time": "06:00"}, {"title": "MF Cast", "time": "06:30"}, {"title": "Campo Vivo", "time": "07:00"}, {"title": "AgroBand", "time": "08:00"}, {"title": "Agromanhã", "time": "08:30"}, {"title": "<PERSON><PERSON><PERSON><PERSON> : T1 EP9 - Treinando para o Torneio de Pesca", "time": "10:30"}, {"title": "Dia Dia Rural", "time": "11:00"}, {"title": "Tatersal", "time": "12:45"}, {"title": "<PERSON><PERSON><PERSON> Brasa", "time": "13:15"}, {"title": "SC Agricultura", "time": "13:45"}, {"title": "Ação Sustentável", "time": "14:15"}, {"title": "Prime Home Decor", "time": "14:30"}, {"title": "Prime Home Decor", "time": "14:30"}, {"title": "Prime Home Decor", "time": "14:30"}, {"title": "Jo<PERSON>", "time": "18:45"}, {"title": "Terraviva DBO na TV", "time": "19:25"}]}, {"name": "The Golf Channel", "logo": "https://tvmap.com.br/images/ch/614.jpg", "programs": [{"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "02:30"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "02:30"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "07:00"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "07:00"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "07:00"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "11:00"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "11:00"}, {"title": "The Open Championship de Golfe 2025 - Segunda Rodada : Segunda Rodada", "time": "15:00"}, {"title": "Live From The Open", "time": "16:30"}, {"title": "Live From The Open", "time": "16:30"}, {"title": "Golfe PGA - Barracuda Championship - Segunda Rodada : Barracuda Championship - Segunda Rodada", "time": "19:00"}, {"title": "Golfe PGA - Barracuda Championship - Segunda Rodada : Barracuda Championship - Segunda Rodada", "time": "19:00"}]}, {"name": "TNT", "logo": "https://tvmap.com.br/images/ch/485.jpg", "programs": [{"title": "Liga Espetacular", "time": "02:26"}, {"title": "Red - Aposentados e Perigosos", "time": "02:46"}, {"title": "O Especialista", "time": "04:34"}, {"title": "Hollywood One on One", "time": "06:00"}, {"title": "Despedida em Grande Estilo", "time": "06:24"}, {"title": "Caçadores de Emoção - Além do Limite", "time": "08:00"}, {"title": "<PERSON><PERSON><PERSON> da Memória", "time": "09:49"}, {"title": "<PERSON><PERSON><PERSON> da Memória", "time": "09:49"}, {"title": "Um Maluco no Pedaço : T3 EP10 - Retorno às Cinzas", "time": "11:47"}, {"title": "Um Maluco no Pedaço : T3 EP11 - Uma Coisa Engraçada no Caminho", "time": "12:10"}, {"title": "<PERSON>do Mundo Odeia o Chris : T3 EP13 - <PERSON><PERSON> Mundo Odeia o Primeiro Beijo", "time": "12:35"}, {"title": "Todo Mundo Odeia o Chris : T3 EP14 - Todo Mundo Odeia a Páscoa", "time": "13:07"}, {"title": "Todo Mundo Odeia o Chris : T3 EP20 - Todo Mundo Odeia o Baile do 9º Ano", "time": "13:38"}, {"title": "17 Outra Vez", "time": "14:10"}, {"title": "17 Outra Vez", "time": "14:10"}, {"title": "Um Senhor E<PERSON>giá<PERSON>", "time": "15:58"}, {"title": "Um Senhor E<PERSON>giá<PERSON>", "time": "15:58"}, {"title": "Stallone - Cobra", "time": "18:03"}, {"title": "Red - Aposentados e Perigosos", "time": "19:35"}, {"title": "Red - Aposentados e Perigosos", "time": "19:35"}]}, {"name": "TNT Séries", "logo": "https://tvmap.com.br/images/ch/657.jpg", "programs": [{"title": "Cold Case : T2 EP6 - Sleepover", "time": "02:13"}, {"title": "Cold Case : T2 EP7 - It's Raining Men", "time": "02:58"}, {"title": "Law & Order: Special Victims Unit : T7 EP13 - Blast", "time": "03:40"}, {"title": "Law & Order: Special Victims Unit : T7 EP14 - Taboo", "time": "04:22"}, {"title": "Major Crimes : T3 EP3 - <PERSON><PERSON><PERSON> Assets", "time": "05:06"}, {"title": "Major Crimes : T3 EP4 - Letting It Go", "time": "05:48"}, {"title": "Person of Interest : T2 EP18 - All In", "time": "06:00"}, {"title": "Person of Interest : T2 EP19 - Trojan Horse", "time": "06:51"}, {"title": "The Closer : T6 EP5 - Heart Attack", "time": "07:33"}, {"title": "The Closer : T6 EP6 - Off the Hook", "time": "08:20"}, {"title": "Major Crimes : T3 EP5 - Do Not Disturb", "time": "09:04"}, {"title": "Major Crimes : T3 EP6 - <PERSON> Number 38", "time": "09:49"}, {"title": "Major Crimes : T3 EP6 - <PERSON> Number 38", "time": "09:49"}, {"title": "The Mentalist : T2 EP21 - 18-5-4", "time": "10:33"}, {"title": "The Mentalist : T2 EP22 - Red Letter", "time": "11:19"}, {"title": "The Mentalist : T2 EP15 - <PERSON>", "time": "12:04"}, {"title": "The Mentalist : T2 EP16 - Code Red", "time": "12:49"}, {"title": "The Mentalist : T2 EP17 - The Red Box", "time": "13:33"}, {"title": "The Mentalist : T2 EP18 - <PERSON><PERSON><PERSON><PERSON>", "time": "14:19"}, {"title": "The Mentalist : T3 EP4 - <PERSON> Carpet Treatment", "time": "15:03"}, {"title": "The Mentalist : T2 EP21 - 18-5-4", "time": "15:47"}, {"title": "The Mentalist : T2 EP22 - Red Letter", "time": "16:32"}, {"title": "The Mentalist : T2 EP15 - <PERSON>", "time": "17:19"}, {"title": "The Mentalist : T2 EP15 - <PERSON>", "time": "17:19"}, {"title": "The Mentalist : T2 EP16 - Code Red", "time": "18:02"}, {"title": "The Mentalist : T2 EP17 - The Red Box", "time": "18:47"}, {"title": "The Mentalist : T2 EP18 - <PERSON><PERSON><PERSON><PERSON>", "time": "19:31"}]}, {"name": "Tooncast", "logo": "https://tvmap.com.br/images/ch/619.jpg", "programs": [{"title": "<PERSON> Hi Puffy Ami<PERSON> : EP38 - <PERSON>alha na Segurança", "time": "02:30"}, {"title": "<PERSON> <PERSON> P<PERSON>y <PERSON> : EP21 - <PERSON> Cansada", "time": "02:37"}, {"title": "ThunderCats", "time": "02:50"}, {"title": "ThunderCats : T1 EP13 - <PERSON><PERSON> Neves", "time": "03:11"}, {"title": "Corrida Maluca : T1 - O Grande Prêmio da Virginia", "time": "03:33"}, {"title": "Corrida Maluca : T1 - O Grande Prêmio do Texas", "time": "03:43"}, {"title": "Os Flintstones : T4 EP9 - <PERSON><PERSON><PERSON> Mais Velho Mai<PERSON> Aprende", "time": "03:55"}, {"title": "Os Flintstones : T6 EP23 - <PERSON><PERSON><PERSON><PERSON>", "time": "04:20"}, {"title": "Os Jetsons : T2 EP23 - <PERSON>", "time": "05:11"}, {"title": "Oswaldo : T2 EP11 - <PERSON><PERSON> <PERSON>", "time": "05:34"}, {"title": "Oswaldo : T2 EP12 - Trabalhópolis", "time": "05:45"}, {"title": "A Mansão Foster para Amigos Imaginários : T4 EP2 - A Fotografia", "time": "06:00"}, {"title": "A Mansão Foster para Amigos Imaginários : T3 EP12 - Um Quarto Disputado", "time": "06:25"}, {"title": "O Acampamento de Lazlo : T2 - Nunca Estive num Submarino", "time": "06:50"}, {"title": "O Acampamento de Lazlo : T4 - Enfermeira-Chefe", "time": "07:01"}, {"title": "O Acampamento de Lazlo : T2 - Olá, Boneca", "time": "07:12"}, {"title": "<PERSON>u Amigo da Escola É um Macaco : <PERSON>", "time": "07:26"}, {"title": "<PERSON>u Amigo da Escola É um Macaco : T1 - Lei e Odor", "time": "07:48"}, {"title": "Esquadrão do Tempo", "time": "08:01"}, {"title": "<PERSON> e <PERSON>", "time": "08:37"}, {"title": "<PERSON>s Filhos de Tom e <PERSON>", "time": "09:22"}, {"title": "<PERSON>", "time": "09:38"}, {"title": "<PERSON><PERSON>", "time": "09:50"}, {"title": "Duck Dodgers", "time": "10:37"}, {"title": "Manda-Chuva : T1 EP10 - Que Rei Sou Eu", "time": "11:02"}, {"title": "Manda-Chuva : T1 EP2 - O Opulento <PERSON>", "time": "11:28"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>? : T2 EP5 - Uma <PERSON> Mal-Assombrada", "time": "11:54"}, {"title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>? : T2 EP7 - <PERSON><PERSON> É <PERSON> com Medo, A<PERSON>?", "time": "12:17"}, {"title": "<PERSON> Que <PERSON>á de Novo Scooby Doo? : T3 - Scooby- Doo no Oeste", "time": "13:00"}, {"title": "Sheep na Cidade Grande", "time": "13:22"}, {"title": "<PERSON>, Lu & Og : T2 - <PERSON><PERSON><PERSON><PERSON>", "time": "13:57"}, {"title": "<PERSON>, Lu & Og : T1 - Ataque Oposto", "time": "14:08"}, {"title": "<PERSON> <PERSON> Puffy Ami<PERSON> : EP14 - <PERSON>las e Caramelos", "time": "14:21"}, {"title": "<PERSON> Hi Puffy Am<PERSON> : EP29 - <PERSON><PERSON> de Visi<PERSON>", "time": "14:28"}, {"title": "<PERSON> Hi Puffy Ami<PERSON>umi : EP45 - Altos e Baixos", "time": "14:35"}, {"title": "ThunderCats", "time": "14:47"}, {"title": "ThunderCats : T1 EP8 - A Torre das Armadilhas", "time": "15:08"}, {"title": "Corrida Maluca : T1 - O Grande Prêmio do Deserto", "time": "15:31"}, {"title": "Corrida Maluca : T1 - Destino Cidade Fantasma", "time": "15:41"}, {"title": "Os Flintstones : T5 EP17 - <PERSON><PERSON> a Quatro Pés", "time": "15:53"}, {"title": "Os Flintstones : T4 EP21 - Um Quarto Não É um Meio", "time": "16:18"}, {"title": "<PERSON><PERSON>", "time": "16:44"}, {"title": "Os Jetsons : T2 EP6 - Problemas Familiares", "time": "17:07"}, {"title": "Oswaldo : T2 EP15 - Ele Não Está Tão Au-Fim de Você", "time": "17:53"}, {"title": "A Mansão Foster para Amigos Imaginários : T4 EP2 - A Fotografia", "time": "18:06"}, {"title": "A Mansão Foster para Amigos Imaginários : T3 EP12 - Um Quarto Disputado", "time": "18:29"}, {"title": "O Acampamento de Lazlo : T2 - Nunca Estive num Submarino", "time": "18:53"}, {"title": "O Acampamento de Lazlo : T4 - Enfermeira-Chefe", "time": "19:04"}, {"title": "O Acampamento de Lazlo : T2 - Olá, Boneca", "time": "19:15"}, {"title": "<PERSON>u Amigo da Escola É um Macaco : <PERSON>", "time": "19:29"}, {"title": "Meu Amigo da Escola É um Macaco : T4 - Sonho do Meio do Semestre", "time": "19:39"}, {"title": "<PERSON>u Amigo da Escola É um Macaco : T1 - Lei e Odor", "time": "19:51"}]}, {"name": "Travel Box Brazil", "logo": "https://tvmap.com.br/images/ch/659.jpg", "programs": [{"title": "<PERSON><PERSON> pelo Mundo : T2 EP13 - Além de Portugal, os Encantos da Paradisíaca Galiza", "time": "02:10"}, {"title": "Errei : T1 EP2 - Morro de São Paulo", "time": "02:40"}, {"title": "Estradas no Travel", "time": "02:55"}, {"title": "Travel Shots : T1 EP76 - Taste Gastronomia São Paulo", "time": "03:10"}, {"title": "Travel Shots : T1 EP77 - Taste Gastronomia São Paulo", "time": "03:25"}, {"title": "Travel Shots : ABAV Expo 2023", "time": "03:35"}, {"title": "Travel Shots", "time": "03:50"}, {"title": "<PERSON><PERSON> pelo <PERSON>", "time": "04:05"}, {"title": "Ponto de Chegada", "time": "05:05"}, {"title": "Expedição Cicloturismo - A Vida em Movimento", "time": "05:35"}, {"title": "<PERSON><PERSON>elo <PERSON> - Nas Águas do Brasil", "time": "06:00"}, {"title": "Cartão de Embarque", "time": "06:30"}, {"title": "As<PERSON> a <PERSON>rdo : T1 EP7 - Disney World em Orlando - Flórida - EUA", "time": "06:45"}, {"title": "Pra Viagem : T2 EP3 - Praia Grande e Cânion Malacara", "time": "07:00"}, {"title": "<PERSON>", "time": "07:15"}, {"title": "<PERSON><PERSON>", "time": "08:00"}, {"title": "Travel Shots : T1 EP76 - Taste Gastronomia São Paulo", "time": "08:15"}, {"title": "Viaje na Melhor Idade : T1 EP1 - Santos", "time": "08:30"}, {"title": "Bella Viagem : T1 EP18 - Hot Beach", "time": "08:45"}, {"title": "Tá Estressado? Vai Viajar! : T5 EP7 - Líbano", "time": "09:15"}, {"title": "Viagem que Segue", "time": "09:30"}, {"title": "BeSisluxe em Portugal", "time": "09:45"}, {"title": "<PERSON><PERSON>elo <PERSON> - Viajar É Divertido", "time": "10:30"}, {"title": "On Board", "time": "11:00"}, {"title": "Vibe de Dois - Rumo ao Alasca : T1 EP3 - Litoral da Argentina", "time": "11:30"}, {"title": "Bar Brasil", "time": "12:00"}, {"title": "<PERSON><PERSON> pelo Mundo e a Magia da Disneyland Paris", "time": "13:00"}, {"title": "Bella Viagem", "time": "13:05"}, {"title": "Viva Essa Viagem", "time": "13:20"}, {"title": "Caçadores dos Ventos", "time": "13:35"}, {"title": "Errei : T1 EP5 - <PERSON><PERSON>", "time": "14:25"}, {"title": "Travel Shots : T1 EP91 - Taste 2024", "time": "14:40"}, {"title": "Medal<PERSON><PERSON>", "time": "15:00"}, {"title": "Medal<PERSON><PERSON>", "time": "15:00"}, {"title": "<PERSON><PERSON>", "time": "19:00"}, {"title": "Mel na Estrada", "time": "19:15"}, {"title": "BeSisluxe em Portugal", "time": "19:30"}]}, {"name": "truTV", "logo": "https://tvmap.com.br/images/ch/615.jpg", "programs": [{"title": "One Piece : T2 EP3 - O Poder Supremo da Santoryu! Zoro vs. Baroque Works!", "time": "02:17"}, {"title": "Os Cavaleiros do Zodíaco : T1 EP67 - <PERSON><PERSON> ao Meu Mestre e aos Meus Amigos", "time": "02:43"}, {"title": "Os Cavaleiros do Zodíaco : T1 EP68 - <PERSON>uerreiro: Afrodite", "time": "03:07"}, {"title": "Death Note : T1 EP25 - <PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "03:33"}, {"title": "<PERSON><PERSON>'s Primal : T1 EP5 - F<PERSON><PERSON> dos Homens-Macaco", "time": "03:56"}, {"title": "Samurai Jack : T1 EP11 - XI", "time": "04:19"}, {"title": "Ambient Swim : T1 EP3 - The Electronic Art of Sound and Light", "time": "05:30"}, {"title": "<PERSON>, <PERSON><PERSON> e Edu : T4 EP2 - Tem um Du na Casa; Nasce um Du", "time": "06:00"}, {"title": "<PERSON>, <PERSON><PERSON> e <PERSON>u : T4 EP3 - <PERSON> no <PERSON>; Um <PERSON> no <PERSON>", "time": "06:23"}, {"title": "<PERSON>, <PERSON><PERSON> e <PERSON>u : T4 EP4 - <PERSON>; <PERSON>", "time": "06:47"}, {"title": "A Vaca e o Frango : T3 EP13 - Vaca Invisível; Monstro no Armário; Eu Sou Cabeleireiro", "time": "07:10"}, {"title": "A Vaca e o Frango : T4 - <PERSON> Wedgie; A Vaca Mais Solitária; <PERSON><PERSON><PERSON><PERSON>hos", "time": "07:57"}, {"title": "<PERSON><PERSON><PERSON>, o Cão Covarde : T1 EP2 - A Sombra do Coragem; Dr<PERSON>, Especialista em Amnésia", "time": "08:19"}, {"title": "Coragem, o Cão Covarde : T1 EP3 - Coragem Encontra o Pé Grande; Cabeça Quente", "time": "08:42"}, {"title": "<PERSON><PERSON><PERSON>, o Cão Covarde : T1 EP4 - <PERSON> Demônio do Colchão; <PERSON>", "time": "09:06"}, {"title": "Teen Titans : T4 EP5 - A Busca", "time": "09:28"}, {"title": "Teen Titans : T4 EP6 - <PERSON><PERSON>", "time": "09:49"}, {"title": "<PERSON> Dez Anos : T2 EP4 - Guarda de Pedestres; <PERSON><PERSON>", "time": "10:11"}, {"title": "Smiling Friends : T2 EP8 - Pim Finalmente Fica Verde", "time": "10:38"}, {"title": "Fugget About It", "time": "10:51"}, {"title": "Teenage Euthanasia : T2 EP7 - A League of His Own", "time": "11:13"}, {"title": "Clone High : T2 EP4 - O Diretor Direto: De Sub-Zero a Sub-Herói", "time": "11:39"}, {"title": "Justiça Jovem : T4 EP14 - Nautical Twilight", "time": "12:05"}, {"title": "Batman Beyond : T3 EP11 - The Curse of the Kobra", "time": "12:52"}, {"title": "<PERSON> : T4 EP2 - P.U.T.A.S.", "time": "13:14"}, {"title": "<PERSON><PERSON> Joven<PERSON> : T5 EP10 - <PERSON><PERSON><PERSON>", "time": "13:37"}, {"title": "<PERSON>s Jovens Titãs : T5 EP11 - <PERSON><PERSON><PERSON> os Titãs", "time": "13:58"}, {"title": "Bravest Warriors : T1 EP1 - Hora do Clime", "time": "14:19"}, {"title": "Blue Lock : T1 EP14 - <PERSON> Geniuses and the Average Joes", "time": "14:41"}, {"title": "Dragon Ball Z : T1 EP255 - Buu Against Buu", "time": "15:29"}, {"title": "Dragon Ball Z : T1 EP256 - Empty Planet", "time": "15:55"}, {"title": "Naruto : T5 EP31 - Voltando à Realidade", "time": "16:19"}, {"title": "Naruto : T5 EP32 - Um Passado a Ser Apagado", "time": "16:42"}, {"title": "One Piece : T1 EP9 - Um Mentiroso com Honra? O Capitão Usopp!", "time": "17:07"}, {"title": "<PERSON><PERSON><PERSON>'s Bizarre Adventure: Diamond Is Unbreakable : T3 EP22 - <PERSON><PERSON><PERSON><PERSON> Quer uma Vida Tranquila", "time": "17:59"}, {"title": "Os Cavaleiros do Zodíaco : T1 EP39 - <PERSON><PERSON><PERSON> o Máscara da Morte", "time": "18:24"}, {"title": "Os Cavaleiros do Zodíaco : T1 EP40 - A Partida", "time": "18:51"}, {"title": "Dragon Ball Z : T1 EP27 - Explode a Fúria de Gohan!", "time": "19:15"}, {"title": "Dragon Ball Z : T1 EP28 - O Ataque dos Sayajins! Kami-<PERSON><PERSON> e <PERSON><PERSON><PERSON>", "time": "19:39"}]}, {"name": "TV Alterosa", "logo": "https://tvmap.com.br/images/ch/687.jpg", "programs": [{"title": "SBT PodNight : <PERSON><PERSON><PERSON> da Coruja", "time": "02:15"}, {"title": "SBT Notícias", "time": "03:00"}, {"title": "SBT Manhã", "time": "04:45"}, {"title": "Igreja Universal", "time": "06:30"}, {"title": "SBT Manhã", "time": "07:35"}, {"title": "Bom Dia & Cia com Patati Patatá", "time": "08:30"}, {"title": "Primeiro Impacto", "time": "09:30"}, {"title": "Primeiro Impacto", "time": "09:30"}, {"title": "Alterosa Agora", "time": "11:00"}, {"title": "Alterosa Esporte", "time": "11:45"}, {"title": "Alterosa Alerta", "time": "12:35"}, {"title": "Alterosa <PERSON>e", "time": "13:20"}, {"title": "A Caverna Encantada", "time": "13:45"}, {"title": "A Usurpadora", "time": "14:30"}, {"title": "Fofocalizando", "time": "15:30"}, {"title": "Me<PERSON> Amor das Estrelas", "time": "16:45"}, {"title": "Tá na Hora", "time": "17:45"}, {"title": "Tá na Hora Minas", "time": "18:30"}, {"title": "Jornal da Alterosa", "time": "19:15"}, {"title": "SBT Brasil", "time": "19:45"}, {"title": "SBT Brasil", "time": "19:45"}]}, {"name": "TV Aparecida", "logo": "https://tvmap.com.br/images/ch/616.jpg", "programs": [{"title": "Medalhão.com", "time": "23:30"}, {"title": "Terço de Aparecida : Terço de Aparecida - Padre Antônio <PERSON>", "time": "03:00"}, {"title": "Família dos Devotos", "time": "03:30"}, {"title": "Ciência sem Limites", "time": "05:00"}, {"title": "Devoto<PERSON>", "time": "06:00"}, {"title": "Terço de Aparecida : Terço de Aparecida - Padre Antônio <PERSON>", "time": "06:15"}, {"title": "Santuário Nacional de Aparecida : Missa", "time": "06:45"}, {"title": "Bênção da Manhã : Geotenda", "time": "07:35"}, {"title": "Ao Redor do Altar", "time": "08:50"}, {"title": "Santuário Nacional de Aparecida : Missa - Tríduo Padre Vitor <PERSON> de Almeida", "time": "09:00"}, {"title": "Família dos Devotos", "time": "10:00"}, {"title": "Diálogos - <PERSON><PERSON> <PERSON>?", "time": "11:30"}, {"title": "Aparecida Interessa ao Brasil", "time": "11:35"}, {"title": "Mensagem de Fé", "time": "11:55"}, {"title": "Santuário Nacional de Aparecida : Missa", "time": "12:00"}, {"title": "Sabor de Vida", "time": "13:00"}, {"title": "Faça Você Mesmo : Mega Artesanal", "time": "14:00"}, {"title": "Viva a Tarde", "time": "15:30"}, {"title": "TJ Aparecida", "time": "16:45"}, {"title": "Basílica Histórica de Nossa Senhora Aparecida : Missa", "time": "18:00"}, {"title": "Terço de Aparecida : Terço de Aparecida - Padre Antônio <PERSON>", "time": "19:00"}, {"title": "Coração Restaurado", "time": "19:30"}, {"title": "Coração Restaurado", "time": "19:30"}]}, {"name": "TV Brasil", "logo": "https://tvmap.com.br/images/ch/387.jpg", "programs": [{"title": "Paisagens Secretas", "time": "02:30"}, {"title": "<PERSON>", "time": "03:30"}, {"title": "Olhar Independente", "time": "06:00"}, {"title": "<PERSON><PERSON>", "time": "06:30"}, {"title": "Tuiga : T1 EP3 - Spa de Inverno", "time": "07:00"}, {"title": "Zoopedia : Tubarão-Lixa", "time": "07:08"}, {"title": "G<PERSON>nha Pintadinha Mini : T1 EP9 - O Planeta Violeta", "time": "07:16"}, {"title": "<PERSON><PERSON>", "time": "07:29"}, {"title": "Gildo : Limpinhos e Cheirosos", "time": "07:53"}, {"title": "Thiago & Isis e os Segredos do Brasil : Festival de Parintins", "time": "08:08"}, {"title": "Pequenos Mestres : Xilogravura", "time": "08:22"}, {"title": "As Microaventuras de Tito e Muda", "time": "08:35"}, {"title": "As Novas Missões do Peixonauta : T2 EP27 - O Caso do Julgamento", "time": "08:43"}, {"title": "Pedrinho e a Chuteira da Sorte", "time": "08:55"}, {"title": "Manual de Sobrevivência da Literatura Brasileira : T1 EP7 - História de Juvenal e o Dragão", "time": "09:07"}, {"title": "O Extraordinário Circo do Bipo : Cerimônia de Premiação", "time": "09:38"}, {"title": "<PERSON><PERSON>, o Mestre Cuca : T1 EP16 - Olha a Pizza!", "time": "09:53"}, {"title": "O Cerrado e Outros Bichos", "time": "10:19"}, {"title": "<PERSON><PERSON>ig<PERSON> : T1 - Um Desenho Só pra Mim", "time": "10:47"}, {"title": "Meu <PERSON>ig<PERSON> : A Princesa Perfeita", "time": "11:01"}, {"title": "O Show da Luna! : T4 EP26 - <PERSON> por Fora, <PERSON><PERSON><PERSON><PERSON> por <PERSON><PERSON>", "time": "11:15"}, {"title": "O Show da Luna! : T5 EP9 - <PERSON><PERSON>, mas N<PERSON>ai", "time": "11:27"}, {"title": "O Show da Luna!", "time": "11:39"}, {"title": "Historietas Assombradas : T2 EP6 - Os Mil e um Pedidos", "time": "11:51"}, {"title": "Historietas Assombradas : T2 EP8 - <PERSON><PERSON> Tem Curativo, <PERSON><PERSON>", "time": "12:04"}, {"title": "<PERSON> e <PERSON> : T1 EP17 - <PERSON><PERSON><PERSON>", "time": "12:17"}, {"title": "Repórter Brasil Tarde", "time": "12:45"}, {"title": "Be<PERSON>", "time": "13:30"}, {"title": "Brasil Visto de Cima", "time": "14:00"}, {"title": "Parques do Brasil", "time": "14:30"}, {"title": "Imensidão Azul", "time": "15:00"}, {"title": "<PERSON><PERSON>", "time": "16:00"}, {"title": "Brasil Visto de Cima", "time": "18:00"}, {"title": "Stadium", "time": "18:30"}, {"title": "Repórter Brasil", "time": "19:00"}, {"title": "Sangue Oculto", "time": "20:00"}]}, {"name": "TV Câmara", "logo": "https://tvmap.com.br/images/ch/386.jpg", "programs": [{"title": "Comissão", "time": "00:00"}, {"title": "Comissão", "time": "00:00"}, {"title": "Qual a Questão?", "time": "08:00"}, {"title": "Comissão", "time": "09:00"}, {"title": "Comissão", "time": "09:00"}, {"title": "Palavra Aberta", "time": "12:00"}, {"title": "Documentário TV Câmara", "time": "13:00"}, {"title": "Comissão", "time": "14:00"}, {"title": "Comissão", "time": "14:00"}, {"title": "Comissão", "time": "14:00"}]}, {"name": "TV Justiça", "logo": "https://tvmap.com.br/images/ch/389.jpg", "programs": [{"title": "Sessão STF", "time": "00:30"}, {"title": "Jornada TST", "time": "03:48"}, {"title": "<PERSON><PERSON><PERSON>", "time": "04:00"}, {"title": "Grandes Julgamentos do STF", "time": "05:30"}, {"title": "<PERSON><PERSON><PERSON> sem Fronteiras", "time": "06:00"}, {"title": "OAB Nacional", "time": "06:30"}, {"title": "Link CNJ", "time": "07:00"}, {"title": "Saber <PERSON>", "time": "08:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "09:00"}, {"title": "Como Funciona Aí", "time": "09:02"}, {"title": "Hora Extra", "time": "09:30"}, {"title": "OAB Entrevistas", "time": "10:30"}, {"title": "Artigo 5º", "time": "11:00"}, {"title": "MP <PERSON><PERSON><PERSON><PERSON>", "time": "11:30"}, {"title": "<PERSON><PERSON><PERSON>", "time": "12:00"}, {"title": "Cinematógrafo", "time": "12:15"}, {"title": "Descomplicando", "time": "13:00"}, {"title": "Documentário", "time": "13:30"}, {"title": "Sessão STF", "time": "14:00"}, {"title": "Sessão STF", "time": "14:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "15:49"}, {"title": "Tá na Web", "time": "18:00"}, {"title": "Jornal da Justiça - 2ª Edição", "time": "18:30"}, {"title": "Como Funciona Aí", "time": "19:00"}, {"title": "Revista TST", "time": "19:30"}]}, {"name": "TV Novo Tempo", "logo": "https://tvmap.com.br/images/ch/649.jpg", "programs": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "02:30"}, {"title": "Reavivados por Sua Palavra", "time": "03:00"}, {"title": "<PERSON><PERSON>", "time": "03:30"}, {"title": "Bíblia Fácil", "time": "04:00"}, {"title": "Reavivados por Sua Palavra", "time": "06:00"}, {"title": "Super Lupa", "time": "06:30"}, {"title": "Está Escrito", "time": "07:00"}, {"title": "Identidade Geral", "time": "08:00"}, {"title": "Vida e Saúde", "time": "08:30"}, {"title": "Caixa de Música", "time": "10:00"}, {"title": "Bíblia Fácil", "time": "11:00"}, {"title": "Revista Novo Tempo", "time": "12:00"}, {"title": "Origens", "time": "13:00"}, {"title": "Super Lupa", "time": "13:30"}, {"title": "<PERSON><PERSON>", "time": "14:00"}, {"title": "Entre Família", "time": "14:30"}, {"title": "Entre Família", "time": "14:30"}, {"title": "Vida e Saúde", "time": "16:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "18:00"}, {"title": "Revista Novo Tempo", "time": "18:45"}, {"title": "Escola Bíblica", "time": "19:30"}]}, {"name": "TV Senado", "logo": "https://tvmap.com.br/images/ch/394.jpg", "programs": [{"title": "O Dia que Durou 21 Anos", "time": "01:35"}, {"title": "<PERSON>", "time": "03:00"}, {"title": "TV Senado Live", "time": "03:30"}, {"title": "Parlamento Brasil", "time": "04:00"}, {"title": "Salão Nobre", "time": "05:30"}, {"title": "1932, A Guerra Civil", "time": "06:00"}, {"title": "Guerras do Brasil.Doc : A Guerra dos Palmares", "time": "07:00"}, {"title": "Cidadania", "time": "08:00"}, {"title": "Parlamento Brasil", "time": "08:30"}, {"title": "TV Senado Live", "time": "09:00"}, {"title": "Sessão Plenária", "time": "09:30"}, {"title": "Sessão Plenária", "time": "09:30"}, {"title": "Sessão Plenária", "time": "09:30"}, {"title": "Senado Notícias : Revista", "time": "13:30"}, {"title": "Reunião de Comissão", "time": "14:00"}, {"title": "Reunião de Comissão", "time": "14:00"}, {"title": "Reunião de Comissão", "time": "14:00"}, {"title": "Senado Notícias : Revista", "time": "19:30"}]}, {"name": "TV5 Monde", "logo": "https://tvmap.com.br/images/ch/564.jpg", "programs": [{"title": "Le 6h00 info", "time": "02:15"}, {"title": "Un soir au music-hall", "time": "02:40"}, {"title": "Sale temps pour la planète : T16 EP4 - Vosges, coup de chaud sur le massif", "time": "04:14"}, {"title": "Destination Francophonie : Québec (Partie 2)", "time": "05:07"}, {"title": "Un si grand soleil", "time": "05:37"}, {"title": "Le journal de Radio-Canada", "time": "06:00"}, {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "time": "06:26"}, {"title": "TV5MONDE, le journal", "time": "07:17"}, {"title": "Tour de France 2025", "time": "08:00"}, {"title": "Le Journal de la RTBF", "time": "09:00"}, {"title": "Femmes d'Afrique en action : T3 EP5 - <PERSON><PERSON><PERSON> (Rwanda)", "time": "09:27"}, {"title": "La vie à vélo : Brest, tandem et aventures accessibles à tous", "time": "12:36"}, {"title": "La vigne est belle : T1 EP2 - Entre tradition et innovation", "time": "13:03"}, {"title": "64' le monde en français - 1re partie", "time": "14:00"}, {"title": "Profs du monde : EP<PERSON> - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "time": "14:24"}, {"title": "64', le monde en français", "time": "14:30"}, {"title": "L'invité", "time": "14:50"}, {"title": "La montagne", "time": "15:05"}, {"title": "Je suis Malika", "time": "16:54"}, {"title": "Le journal de la RTS", "time": "17:00"}, {"title": "Questions pour un champion", "time": "17:27"}, {"title": "TV5Monde, le journal Afrique", "time": "18:00"}, {"title": "Tendance XXI : T9 EP3 - Chargeurs réunis / <PERSON><PERSON> re<PERSON> / MHP marqueterie <PERSON> / <PERSON><PERSON> capuche / <PERSON>", "time": "18:30"}, {"title": "Via Idra, entre terre et ciel : T1 EP2 - Du Campo Tencia à la cabane Cognora", "time": "19:00"}]}, {"name": "TVE Internacional", "logo": "https://tvmap.com.br/images/ch/565.jpg", "programs": [{"title": "Telediario Matinal", "time": "02:30"}, {"title": "La hora de la 1", "time": "03:30"}, {"title": "La hora de la 1", "time": "03:30"}, {"title": "Mañaneros 360", "time": "05:35"}, {"title": "Mañaneros 360", "time": "05:35"}, {"title": "Mañaneros 360", "time": "09:20"}, {"title": "Telediario 1", "time": "10:00"}, {"title": "Deportes 1", "time": "10:35"}, {"title": "Zoom Net", "time": "10:40"}, {"title": "Malas lenguas", "time": "10:55"}, {"title": "Ruralitas", "time": "12:20"}, {"title": "Ruralitas", "time": "12:20"}, {"title": "<PERSON>ber y ganar", "time": "13:15"}, {"title": "Malas lenguas", "time": "14:00"}, {"title": "Malas lenguas", "time": "14:00"}, {"title": "Telediario 2", "time": "16:00"}, {"title": "Deportes 2", "time": "16:45"}, {"title": "Cifras y letras", "time": "16:50"}, {"title": "Valle Salvaje", "time": "17:20"}, {"title": "Valle Salvaje", "time": "17:20"}, {"title": "La promesa", "time": "18:05"}, {"title": "El cazador", "time": "18:55"}, {"title": "Aquí la Tierra", "time": "19:50"}, {"title": "Aquí la Tierra", "time": "19:50"}]}, {"name": "Warner Channel", "logo": "https://tvmap.com.br/images/ch/507.jpg", "programs": [{"title": "The Big Bang Theory : T3 EP17 - The Precious Fragmentation", "time": "02:20"}, {"title": "The Big Bang Theory : T3 EP18 - The Pants Alternative", "time": "02:40"}, {"title": "The Big Bang Theory : T3 EP19 - The Wheaton Recurrence", "time": "03:03"}, {"title": "The Big Bang Theory : T3 EP20 - The Spaghetti Catalyst", "time": "03:25"}, {"title": "The Vampire Diaries : T2 EP3 - Bad Moon Rising", "time": "03:45"}, {"title": "All American : T6 EP15 - <PERSON> <PERSON> (Part II)", "time": "05:15"}, {"title": "Friends : T7 EP1 - The One With Monica's Thunder", "time": "06:00"}, {"title": "Two and a Half Men : T5 EP18 - If My Hole Could Talk", "time": "06:50"}, {"title": "Two and a Half Men : T5 EP19 - Waiting for the Right Snapper", "time": "07:13"}, {"title": "Two and a Half Men : T6 EP2 - <PERSON>, <PERSON>", "time": "07:59"}, {"title": "Two and a Half Men : T6 EP3 - Damn You, <PERSON><PERSON>", "time": "08:23"}, {"title": "Two and a Half Men : T6 EP4 - The Flavin' and the Mavin", "time": "08:45"}, {"title": "The Vampire Diaries : T2 EP5 - <PERSON> or Be Killed", "time": "09:07"}, {"title": "The Vampire Diaries : T2 EP6 - Plan B", "time": "09:54"}, {"title": "The Vampire Diaries : T2 EP6 - Plan B", "time": "09:54"}, {"title": "<PERSON> : T3 EP9 - A Party Invitation, Football Grapes and an Earth Chicken", "time": "10:39"}, {"title": "<PERSON> : T3 EP10 - Teenage<PERSON> and a Little Ball of Fib", "time": "11:01"}, {"title": "<PERSON> Sheldon : T3 EP11 - A Live Chicken, a Fried Chicken and Holy Matrimony", "time": "11:22"}, {"title": "<PERSON> Sheldon : T3 EP12 - Body Glitter and a Mall Safety Kit", "time": "11:43"}, {"title": "The Big Bang Theory : T3 EP21 - The Plimpton Stimulation", "time": "12:02"}, {"title": "The Big Bang Theory : T3 EP22 - The Staircase Implementation", "time": "12:26"}, {"title": "The Big Bang Theory : T3 EP23 - The Lunar Excitation", "time": "12:47"}, {"title": "The Big Bang Theory : T4 EP1 - The Robotic Manipulation", "time": "13:07"}, {"title": "The Big Bang Theory : T4 EP2 - The Cruciferous Vegetable Amplification", "time": "13:32"}, {"title": "The Big Bang Theory : T4 EP3 - The Zazzy Substitution", "time": "13:56"}, {"title": "Two and a Half Men : T5 EP18 - If My Hole Could Talk", "time": "14:20"}, {"title": "Two and a Half Men : T5 EP19 - Waiting for the Right Snapper", "time": "14:43"}, {"title": "Two and a Half Men : T6 EP2 - <PERSON>, <PERSON>", "time": "15:30"}, {"title": "Two and a Half Men : T6 EP3 - Damn You, <PERSON><PERSON>", "time": "15:55"}, {"title": "Two and a Half Men : T6 EP4 - The Flavin' and the Mavin", "time": "16:19"}, {"title": "Cobra Kai : T2 EP4 - <PERSON><PERSON>ade", "time": "16:40"}, {"title": "The Vampire Diaries : T2 EP5 - <PERSON> or Be Killed", "time": "17:17"}, {"title": "The Vampire Diaries : T2 EP5 - <PERSON> or Be Killed", "time": "17:17"}, {"title": "The Vampire Diaries : T2 EP6 - Plan B", "time": "18:01"}, {"title": "<PERSON> : T3 EP9 - A Party Invitation, Football Grapes and an Earth Chicken", "time": "18:50"}, {"title": "<PERSON> : T3 EP10 - Teenage<PERSON> and a Little Ball of Fib", "time": "19:10"}, {"title": "<PERSON> Sheldon : T3 EP11 - A Live Chicken, a Fried Chicken and Holy Matrimony", "time": "19:31"}, {"title": "<PERSON> Sheldon : T3 EP12 - Body Glitter and a Mall Safety Kit", "time": "19:54"}]}, {"name": "Woohoo", "logo": "https://tvmap.com.br/images/ch/561.jpg", "programs": [{"title": "1001 Noites", "time": "00:00"}, {"title": "Do Alto", "time": "04:00"}, {"title": "Giro GG", "time": "04:10"}, {"title": "Short Facts", "time": "04:20"}, {"title": "Snap", "time": "05:30"}, {"title": "TBT", "time": "05:40"}, {"title": "Projeto Califa", "time": "05:50"}, {"title": "Saindo na Trip", "time": "06:00"}, {"title": "Eu Mega Recomendo", "time": "06:30"}, {"title": "<PERSON><PERSON>das", "time": "07:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "08:00"}, {"title": "Free Surf", "time": "08:15"}, {"title": "<PERSON><PERSON>", "time": "08:45"}, {"title": "Check Point", "time": "09:00"}, {"title": "Queue Games", "time": "09:15"}, {"title": "Esse Jogo Não Tem Pause", "time": "09:30"}, {"title": "D.N.A.", "time": "10:30"}, {"title": "Streamerland", "time": "11:00"}, {"title": "Esse Jogo Não Tem Pause", "time": "12:00"}, {"title": "Luara e o Mar", "time": "13:00"}, {"title": "<PERSON><PERSON><PERSON>", "time": "13:15"}, {"title": "Custom : T8 EP4 - <PERSON> Nails", "time": "13:30"}, {"title": "Queue", "time": "13:45"}, {"title": "Top Ranking", "time": "14:00"}, {"title": "Locals : T1 EP2 - <PERSON><PERSON>", "time": "14:15"}, {"title": "Viajando pelo Brasil", "time": "14:30"}, {"title": "Woohoo Music", "time": "15:30"}, {"title": "Onboarding Tribos", "time": "15:45"}, {"title": "Pipoca no Set", "time": "16:00"}, {"title": "<PERSON><PERSON>", "time": "16:15"}, {"title": "Free Surf", "time": "16:19"}, {"title": "Queue Staff Picks", "time": "16:23"}, {"title": "Gibigice", "time": "16:27"}, {"title": "Hootalks", "time": "16:30"}, {"title": "Streamerland", "time": "17:00"}, {"title": "Queue Games", "time": "18:00"}, {"title": "Locals : T1 EP2 - <PERSON><PERSON>", "time": "18:15"}, {"title": "<PERSON><PERSON>", "time": "18:30"}, {"title": "Pipoca no Set", "time": "19:00"}, {"title": "Top Ranking", "time": "19:15"}, {"title": "Som na Lata", "time": "19:30"}]}, {"name": "Zoomoo", "logo": "https://tvmap.com.br/images/ch/691.jpg", "programs": [{"title": "Zoomoo Show", "time": "02:26"}, {"title": "Zoomoo Show", "time": "04:50"}, {"title": "Flash, o Aventureiro : T1 EP24 - <PERSON><PERSON>", "time": "06:04"}, {"title": "Flash, o Aventureiro : T1 EP25 - <PERSON><PERSON><PERSON>", "time": "06:11"}, {"title": "Flash, o Aventureiro : T1 EP26 - <PERSON><PERSON> Voadora", "time": "06:18"}, {"title": "Flash, o Aventureiro : Foca", "time": "06:25"}, {"title": "Flash, o Aventureiro : T1 EP2 - Girafa", "time": "06:33"}, {"title": "<PERSON>, o Aventureiro : T1 EP3 - Hipopótamo", "time": "06:40"}, {"title": "Flash, o Aventureiro : T1 EP4 - <PERSON><PERSON><PERSON>", "time": "06:47"}, {"title": "Flash, o Aventureiro : T1 EP5 - Macaco", "time": "06:54"}, {"title": "Vamos Falar Sobre Valores : T1 EP5 - A Serenidade", "time": "07:01"}, {"title": "Vamos Falar Sobre Valores : T1 EP7 - A Dedicação", "time": "07:03"}, {"title": "As Novas Missões do Peixonauta : T2 EP17 - O Caso Ovoide", "time": "07:05"}, {"title": "Animazoo : T1 EP17 - <PERSON><PERSON><PERSON>", "time": "07:17"}, {"title": "Animazoo : T1 EP1 - Heróis do Parque", "time": "07:23"}, {"title": "Turma do Gato Galáctico", "time": "08:00"}, {"title": "Lupita Curiosidades : T1 EP2 - Qual Será a Música da França?", "time": "08:17"}, {"title": "Lupita no Planeta de Gente Grande : T2 EP1 - Amazônia", "time": "08:19"}, {"title": "Lupita Curiosidades : T1 EP3 - Voc<PERSON> o que É uma Capital?", "time": "08:23"}, {"title": "Lupita no Planeta de Gente Grande : T2 EP2 - Austrália", "time": "08:25"}, {"title": "Lupita Curiosidades : T1 EP4 - Paris em Verso", "time": "08:27"}, {"title": "Os Chocolix : T1 EP6 - Uma Festa Mágica", "time": "08:30"}, {"title": "Os Chocolix : T1 EP7 - A Fadinha Florida", "time": "08:38"}, {"title": "Rancho Colorido : T1 EP7 - <PERSON> Ovelha de Lentes", "time": "08:45"}, {"title": "Rancho Colorido : T1 EP17 - <PERSON><PERSON><PERSON><PERSON> da Ovelha de Lentes", "time": "08:49"}, {"title": "Rancho Colorido : T1 EP8 - O Avestruzão", "time": "08:52"}, {"title": "Rancho Colorido : T1 EP18 - Videoclipe do Avestruzão", "time": "08:55"}, {"title": "Que Animal É Esse? : T1 EP10 - Tartaruga", "time": "09:00"}, {"title": "Que Animal É Esse? : T1 EP11 - Elefante", "time": "09:02"}, {"title": "Que Animal É Esse? : T1 EP12 - Girafa", "time": "09:03"}, {"title": "Que Animal É Esse? : T1 EP13 - <PERSON><PERSON>", "time": "09:05"}, {"title": "Que Animal É Esse? : T1 EP1 - <PERSON><PERSON>", "time": "09:07"}, {"title": "Que Animal É Esse? : T1 EP2 - Capivara", "time": "09:08"}, {"title": "Que Animal É Esse? : T1 EP3 - Cachor<PERSON>", "time": "09:10"}, {"title": "Hora do Jacarelvis e Amigos", "time": "09:14"}, {"title": "<PERSON><PERSON> - <PERSON> Terra dos Dinossauros : T3 EP1 - O Diplodoco", "time": "09:30"}, {"title": "<PERSON><PERSON> - <PERSON> Terra dos Dinossauros : T3 EP7 - Música do Diplodoco", "time": "09:33"}, {"title": "<PERSON><PERSON> - <PERSON> Terra dos Dinossauros : T3 EP2 - <PERSON> Dilofossauro", "time": "09:37"}, {"title": "<PERSON><PERSON> - <PERSON> Terra dos Dinossauros : T3 EP8 - Música do Dilofossauro", "time": "09:40"}, {"title": "<PERSON><PERSON> - <PERSON> Terra dos Dinossauros : T3 EP10 - Música do Elasmossauro", "time": "09:43"}, {"title": "Astrobaldo : <PERSON><PERSON>", "time": "09:47"}, {"title": "Educadinho : T1 - <PERSON> <PERSON>", "time": "10:30"}, {"title": "Educadinho : T1 - <PERSON><PERSON><PERSON><PERSON>", "time": "10:33"}, {"title": "Mundo de Kaboo : T3 EP25 - <PERSON><PERSON><PERSON><PERSON> de Bo<PERSON>ha", "time": "10:36"}, {"title": "O Incrível Mundo da Imaginação : T1 EP10 - Uma Casa na Montanha", "time": "10:50"}, {"title": "O Incrível Mundo da Imaginação : T1 EP20 - Videoclipe - Uma Casa na Montanha", "time": "10:53"}, {"title": "O Incrível Mundo da Imaginação : T1 EP1 - O Bosque dos Cogumelos Mágicos", "time": "10:57"}, {"title": "O Incrível Mundo da Imaginação : T1 EP11 - Videoclipe - O Bosque de Cogumelos Mágicos", "time": "11:00"}, {"title": "Meu AmigãoZão : T1 - Dia do Amigo", "time": "11:04"}, {"title": "A Cozinha do Chef Luca : T1 EP2 - Panqueca de Banana", "time": "11:16"}, {"title": "A Cozinha do Chef Luca : T1 EP3 - Crepioca", "time": "11:19"}, {"title": "A Cozinha do Chef Luca : T1 EP4 - <PERSON><PERSON><PERSON>ão", "time": "11:21"}, {"title": "A Cozinha do Chef Luca : T1 EP5 - Brigadeiro Light", "time": "11:23"}, {"title": "A Cozinha do Chef Luca : T1 EP6 - Bolo Diet de Banana", "time": "11:26"}, {"title": "Patatoons : T1 EP1 - Universo", "time": "11:30"}, {"title": "Patati Patatá : Fantasia", "time": "11:31"}, {"title": "Patati Patatá - O Melhor da Pré-Escola : O Sapo Não Lava o Pé", "time": "11:33"}, {"title": "Patati Patatá : <PERSON><PERSON><PERSON>", "time": "11:36"}, {"title": "Patati Patatá - Coletâneas de Sucesso : T1 EP9 - Lua de Cristal", "time": "11:38"}, {"title": "Patati Patatá - <PERSON> Melhor da Pré Escola 2 : T1 EP2 - Ciranda Cirandinha", "time": "11:42"}, {"title": "Paleokids : T1 EP8 - Mamutes", "time": "11:45"}, {"title": "Vamos Falar Sobre Valores : T1 EP9 - A Compreensão", "time": "11:55"}, {"title": "Vamos Falar Sobre Valores : T1 EP11 - A Amizade", "time": "11:57"}, {"title": "Turma do Gato Galáctico", "time": "12:00"}, {"title": "As Novas Missões do Peixonauta : T2 EP17 - O Caso Ovoide", "time": "12:17"}, {"title": "Turma do Folclore : T10 EP4 - Cuidando do Meio Ambiente - Videoclipe Animais", "time": "13:00"}, {"title": "Turma do Folclore : T9 EP2 - O Incêndio no Galpão", "time": "13:03"}, {"title": "Turma do Folclore : T8 EP6 - Salvando a Lagoa", "time": "13:06"}, {"title": "Turma do Folclore : T6 EP6 - <PERSON><PERSON><PERSON> da Matinta Pereira", "time": "13:12"}, {"title": "Herói do Bairro : T1 EP1 - O Gatinho Sumiu", "time": "13:14"}, {"title": "Herói do Bairro : T1 EP11 - A Missão do Gatinho", "time": "13:18"}, {"title": "Herói do Bairro : T1 EP3 - O Sumiço da Torta da Mamãe", "time": "13:23"}, {"title": "Herói do Bairro : T1 EP13 - A Missão da Torta", "time": "13:26"}, {"title": "Mundo de Kaboo : T3 EP25 - <PERSON><PERSON><PERSON><PERSON> de Bo<PERSON>ha", "time": "13:30"}, {"title": "Rancho Colorido : T1 EP7 - <PERSON> Ovelha de Lentes", "time": "13:45"}, {"title": "Rancho Colorido : T1 EP17 - <PERSON><PERSON><PERSON><PERSON> da Ovelha de Lentes", "time": "13:48"}, {"title": "Rancho Colorido : T1 EP8 - O Avestruzão", "time": "13:51"}, {"title": "Rancho Colorido : T1 EP18 - Videoclipe do Avestruzão", "time": "13:55"}, {"title": "Tubarão Martelo e os Habitantes do Fundo do Mar : T1 EP8 - Ouriço, Caranguejo, Polvo e Lula", "time": "14:00"}, {"title": "Tubarão Martelo : T1 EP9 - <PERSON><PERSON><PERSON>", "time": "14:02"}, {"title": "Tubarão Martelo : T1 EP5 - Golfinhos", "time": "14:05"}, {"title": "Tubarão Martelo : T1 EP7 - Pequenas Sereias", "time": "14:08"}, {"title": "Flash, o Aventureiro : T1 EP23 - Zebra", "time": "14:15"}, {"title": "Flash, o Aventureiro : T1 EP24 - <PERSON><PERSON>", "time": "14:22"}, {"title": "Flash, o Aventureiro : T1 EP25 - <PERSON><PERSON><PERSON>", "time": "14:30"}, {"title": "Flash, o Aventureiro : T1 EP26 - <PERSON><PERSON> Voadora", "time": "14:37"}, {"title": "Conta Comigo : Se<PERSON> Manual; <PERSON>", "time": "14:44"}, {"title": "<PERSON><PERSON><PERSON>, o Panda Azul : T1 EP11 - Brincando no Escuro", "time": "14:56"}, {"title": "<PERSON><PERSON><PERSON>, o Panda Azul : T1 EP23 - Videoclipe - Brincando no Escuro", "time": "14:59"}, {"title": "Educadinho : T1 - Hora do Banho", "time": "15:29"}, {"title": "O Diário de Mika : T1 EP35 - <PERSON> Outono", "time": "15:32"}, {"title": "<PERSON> Diário de Mika : T1 EP36 - <PERSON><PERSON><PERSON> de Ovelha", "time": "15:40"}, {"title": "Freely - Apr<PERSON> <PERSON> : T1 EP9 - <PERSON><PERSON><PERSON>?", "time": "15:48"}, {"title": "Freely - Apr<PERSON> <PERSON> : T1 EP10 - <PERSON><PERSON>", "time": "15:52"}, {"title": "Freely - Apr<PERSON> <PERSON> : T1 EP11 - <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON> e <PERSON> na Feira", "time": "15:56"}, {"title": "Freely - Apr<PERSON> <PERSON> : T1 EP12 - <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>", "time": "16:00"}, {"title": "As Novas Missões do Peixonauta : T2 EP17 - O Caso Ovoide", "time": "16:04"}, {"title": "Os Chocolix : T1 EP6 - Uma Festa Mágica", "time": "16:16"}, {"title": "Os Chocolix : T1 EP7 - A Fadinha Florida", "time": "16:24"}, {"title": "Patatoons : T1 EP4 - <PERSON><PERSON> pra que te Quero", "time": "16:32"}, {"title": "Patati Patatá : Carr<PERSON>el", "time": "16:33"}, {"title": "Patati Patatá - O Melhor da Pré-Escola : <PERSON><PERSON><PERSON>", "time": "16:36"}, {"title": "Vem Cantar com Patati e Patatá - Clipes Musicais : T1 EP23 - A Formiguinha", "time": "16:39"}, {"title": "Patati Patatá - Coletâneas de Sucesso : T1 EP3 - <PERSON>", "time": "16:40"}, {"title": "<PERSON><PERSON> Patatá - <PERSON> Melhor da Pré Escola 2 : T1 EP8 - <PERSON><PERSON><PERSON>", "time": "16:44"}, {"title": "Meu AmigãoZão : T1 - Dia do Amigo", "time": "16:47"}, {"title": "Vamos Falar Sobre Valores : T1 EP13 - A Honestidade", "time": "16:59"}, {"title": "Vamos Falar Sobre Valores : T1 EP15 - A Perseverança", "time": "17:01"}, {"title": "Je<PERSON>m : T2 EP4 - <PERSON><PERSON>", "time": "17:04"}, {"title": "Malu na Cidade das Emoções : T3 EP5 - A Ansiedade", "time": "17:16"}, {"title": "Malu na Cidade das Emoções : T3 EP11 - Ansiedade", "time": "17:19"}, {"title": "Malu na Cidade das Emoções : T3 EP6 - A Vergonha", "time": "17:22"}, {"title": "Malu na Cidade das Emoções : T3 EP12 - Vergonha", "time": "17:26"}, {"title": "O Diário de Mika : T1 EP35 - <PERSON> Outono", "time": "18:00"}, {"title": "<PERSON> Diário de Mika : T1 EP36 - <PERSON><PERSON><PERSON> de Ovelha", "time": "18:08"}, {"title": "Animazoo : T1 EP17 - <PERSON><PERSON><PERSON>", "time": "18:16"}, {"title": "Animazoo : T1 EP1 - Heróis do Parque", "time": "18:22"}, {"title": "Turma do Folclore : T10 EP4 - Cuidando do Meio Ambiente - Videoclipe Animais", "time": "18:29"}, {"title": "Turma do Folclore : T9 EP2 - O Incêndio no Galpão", "time": "18:32"}, {"title": "Turma do Folclore : T8 EP6 - Salvando a Lagoa", "time": "18:35"}, {"title": "Turma do Folclore : T6 EP6 - <PERSON><PERSON><PERSON> da Matinta Pereira", "time": "18:41"}, {"title": "Mansão Halloween : T1 EP9 - <PERSON><PERSON><PERSON>", "time": "18:45"}, {"title": "Mansão Halloween : T1 EP10 - <PERSON><PERSON>tal<PERSON>", "time": "18:48"}, {"title": "Mansão Halloween : T1 EP11 - <PERSON><PERSON><PERSON> da Bruxinha", "time": "18:53"}, {"title": "Mansão Halloween : T1 EP12 - <PERSON>ite de Halloween", "time": "18:56"}, {"title": "Paleokids : T1 EP1 - Preguiça-Gigante", "time": "19:00"}, {"title": "Vamos Falar Sobre Valores : T1 EP17 - A Responsabilidade", "time": "19:11"}, {"title": "Vamos Falar Sobre Valores : T1 EP19 - A Justiça", "time": "19:13"}, {"title": "<PERSON> e <PERSON> : T3 EP9 - <PERSON>", "time": "19:15"}, {"title": "Patatoons : T1 EP7 - <PERSON><PERSON><PERSON><PERSON>", "time": "19:30"}, {"title": "Patati Patatá : O Ronco do Vovô", "time": "19:31"}, {"title": "Patati Patatá - O Melhor da Pré-Escola : A Dona Baratinha", "time": "19:34"}, {"title": "Patati Patatá - O Melhor da Pré-Escola : Atchim!", "time": "19:37"}, {"title": "Patati Patatá - Coletâneas de Sucesso : T1 EP8 - Festa dos Insetos", "time": "19:38"}, {"title": "Patati Patatá - O Melhor da Pré Escola 2 : T1 EP7 - Caranguejo Não É Peixe", "time": "19:41"}, {"title": "Que Animal É Esse? : T1 EP10 - Tartaruga", "time": "19:45"}, {"title": "Que Animal É Esse? : T1 EP11 - Elefante", "time": "19:46"}, {"title": "Que Animal É Esse? : T1 EP12 - Girafa", "time": "19:48"}, {"title": "Que Animal É Esse? : T1 EP13 - <PERSON><PERSON>", "time": "19:50"}, {"title": "Que Animal É Esse? : T1 EP1 - <PERSON><PERSON>", "time": "19:51"}, {"title": "Que Animal É Esse? : T1 EP2 - Capivara", "time": "19:53"}, {"title": "Que Animal É Esse? : T1 EP3 - Cachor<PERSON>", "time": "19:54"}, {"title": "Flash, o Aventureiro : T1 EP2 - Girafa", "time": "19:59"}]}, {"name": "Canal Universitário", "logo": "https://tvmap.com.br/images/ch/395.jpg", "programs": [{"title": "Ciência sem Limites : Pesquisas em Doenças dos Suínos", "time": "06:30"}, {"title": "Programação Canal Universitário", "time": "07:00"}, {"title": "Programação Canal Universitário", "time": "07:00"}, {"title": "Programação Canal Universitário", "time": "10:30"}, {"title": "Caminhos - Q<PERSON>do <PERSON>hos Encontram a Educação : Não Existe Não para Ana Raquel", "time": "11:00"}, {"title": "Programação Canal Universitário", "time": "15:30"}, {"title": "Programação Canal Universitário", "time": "15:30"}, {"title": "PET Discute : IA no Cotidiano Visual", "time": "19:00"}, {"title": "Programação Canal Universitário", "time": "19:30"}, {"title": "Programação Canal Universitário", "time": "19:30"}, {"title": "Programação Canal Universitário", "time": "19:30"}]}]}