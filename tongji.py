import pandas as pd
from sqlalchemy import create_engine
from fuzzywuzzy import fuzz
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_excel_data(file_path):
    try:
        df = pd.read_excel(file_path)
        return df
    except Exception as e:
        logging.error(f"Error loading Excel file: {str(e)}")
        return None

def get_db_data():
    try:
        # 创建数据库连接
        engine = create_engine('mysql+pymysql://spiderman:ew4%2598fRpe@43.157.134.155:33070/spider')
        
        # 查询tvmap_jmd表
        tvmap_query = "SELECT title FROM tvmap_jmd"
        tvmap_df = pd.read_sql(tvmap_query, engine)
        
        # 查询cxtv_live_streaming表
        live_query = "SELECT name FROM cxtv_live_streaming"
        live_df = pd.read_sql(live_query, engine)
        
        return tvmap_df, live_df
    except Exception as e:
        logging.error(f"Error fetching database data: {str(e)}")
        return None, None

def fuzzy_match(source_name, target_names, threshold=80):
    best_match = None
    best_ratio = 0
    
    for target in target_names:
        # 跳过空值
        if pd.isna(target) or not isinstance(target, str):
            continue
            
        # 计算相似度
        try:
            ratio = fuzz.ratio(source_name.lower(), target.lower())
            if ratio > threshold and ratio > best_ratio:
                best_ratio = ratio
                best_match = target
        except Exception as e:
            logging.error(f"Error calculating fuzzy match: {str(e)}")
            continue
    
    return best_match, best_ratio

def main():
    # 加载Excel文件
    excel_path = "匹配表.xlsx"
    df = load_excel_data(excel_path)
    if df is None:
        return
    
    # 获取数据库数据
    tvmap_df, live_df = get_db_data()
    if tvmap_df is None or live_df is None:
        return
    
    # 统计计数器
    tvmap_matches = 0
    live_matches = 0
    total_channels = len(df)
    
    # 遍历Excel中的每个频道
    for index, row in df.iterrows():
        channel_name = row['频道名称']
        
        # 匹配tvmap_jmd表数据
        tvmap_match, tvmap_ratio = fuzzy_match(channel_name, tvmap_df['title'])
        if tvmap_match is not None:
            df.at[index, '节目单(ok)'] = 'ok'
            tvmap_matches += 1
            logging.info(f"TVMap matched: {channel_name} -> {tvmap_match} (ratio: {tvmap_ratio})")
        
        # 匹配cxtv_live_streaming表数据
        live_match, live_ratio = fuzzy_match(channel_name, live_df['name'])
        if live_match is not None:
            df.at[index, '直播源(拿到填ok)'] = 'ok'
            live_matches += 1
            logging.info(f"Live streaming matched: {channel_name} -> {live_match} (ratio: {live_ratio})")
    
    # 保存更新后的Excel文件
    df.to_excel(excel_path, index=False)
    
    # 输出统计结果
    logging.info("\n匹配结果统计:")
    logging.info(f"总频道数: {total_channels}")
    logging.info(f"节目单匹配成功: {tvmap_matches} 个")
    logging.info(f"直播源匹配成功: {live_matches} 个")
    logging.info(f"节目单匹配率: {tvmap_matches/total_channels*100:.2f}%")
    logging.info(f"直播源匹配率: {live_matches/total_channels*100:.2f}%")

if __name__ == "__main__":
    main()