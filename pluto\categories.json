{"data": [{"id": "618da8d65a960200074a9663", "name": "Pluto TV", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618da8d65a960200074a9663/pngImageUrl-1636825170138.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618da8d65a960200074a9663/svgImageUrl-1636825173523.svg"}], "channelIDs": ["5fa97a8a75cc210007c9041d"], "channelSlugs": ["guia-de-canais"]}, {"id": "618f050f4a270700077be26e", "name": "Filmes", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618f050f4a270700077be26e/pngImageUrl-1636826614290.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618f050f4a270700077be26e/svgImageUrl-1636826619620.svg"}], "channelIDs": ["5f120e94a5714d00074576a1", "5f120f41b7d403000783a6d6", "5f12111c9e6c2c00078ef3bb", "5f171d3442a0500007362f22", "5fa15ad6367e170007cdd098", "6479ff764f5ba5000878dfe2", "6806d62369aec5b19cd628c0", "5f12101f0b12f00007844c7c", "62545ed3dab4380007582f7c", "5f171f988ab9780007fa95ea", "5f1210d14ae1f80007bafb1d", "663b9dc7cb3ea10008f1a0ce", "5fa1612a669ba0000702017b", "66aa67493a4ad2000806d91b", "61b790b985706b00072cb797", "66c79a4262e5510008ff68a5", "5f171f032cd22e0007f17f3d", "5fa991b1f09e020007e78626", "5f5a545d0dbf7f0007c09408", "663b9de4f999220008230fa8", "62c5d32e2c48f9000715b6e9", "633dcebd80386500074a2461"], "channelSlugs": ["pluto-tv-cine-sucessos", "pluto-tv-filmes-acao", "pluto-tv-cine-terror-1", "pluto-tv-filmes-suspense", "ficcao-cientifica", "pluto-tv-cine-crime", "pluto-tv-filmes-de-luta", "pluto-tv-cine-comedia-1", "pluto-tv-cine-comedia-romantica", "pluto-tv-cine-romance-ptv1", "pluto-tv-cine-drama-1", "pluto-tv-bang-bang", "pluto-tv-cine-classicos", "pluto-tv-terror-trash", "pluto-tv-adrenalina-freezone", "pluto-tv-filmes-aventura", "pluto-tv-cine-familia-ptv1", "pluto-tv-cine-inspiracao", "pluto-tv-filmes-nacionais", "pluto-tv-netmovies", "runtime", "plutotv-filmelier"]}, {"id": "643f07f72858cb0008a64c37", "name": "Séries", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f07f72858cb0008a64c37/pngImageUrl.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f07f72858cb0008a64c37/svgImageUrl.svg"}], "channelIDs": ["6474ab5cdc7a760008745008", "63eb9c5351f5d000085e8d7e", "63eb9fdda995710008991c54", "67f960c5441853fe50e7afc1", "6474ab1da51cb80008bfb5f4", "64ff2d8c6625510008c5a512", "64ff2c773efb5100082efb73", "63d2ba2f60bc8f0008981a0e", "66b3af48d2d50d00083d6936", "67e59a6557487a8b7fe73e23", "65f060d84e01740008d7421f", "655e5bc94261ca000810cb17", "5ff768b6a4c8b80008498610", "60a8126a0ac3970007f850fe", "613fd0043ffa39000736bb2b", "60f5fabf0721880007cd50e3", "620ff46e0a576e0007dc2f89", "5ffcc5130fd98c0007f2e216", "64e501eec630a900088ed0f8"], "channelSlugs": ["pluto-tv-series-criminais", "csi-miami-ptv4", "ncis-ptv1", "numbers", "pluto-tv-series-acao", "rookie-blue-ptv1", "inferno-sobre-rodas", "pluto-tv-series-sci-fi", "z-nation-ptv1", "relic-hunter-ptv1", "pluto-tv-series-drama-ptv1", "pluto-tv-series-comedia", "bet-pluto-tv-ptv1", "bet-being-mary-jane", "younger-ptv1", "nick-teen-ptv1", "nickelodeon-icarly-ptv1", "kenan-and-kel", "clube-do-terror"]}, {"id": "643f0a3d939a59000826ffa7", "name": "<PERSON><PERSON><PERSON>", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0a3d939a59000826ffa7/pngImageUrl.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0a3d939a59000826ffa7/svgImageUrl.svg"}], "channelIDs": ["5f1212ad1728050007a523b8", "631fa8dd7f25240007099a40", "61f1d27a189ed10007b7393e", "63eb9dc84e83e70008abea92", "62052d3b4eeb740007fbe125", "655e5c4d2c46f3000877a54b", "677d93f37bffa600080795e7"], "channelSlugs": ["pluto-tv-retro-2", "a-feiticeira", "diffrent-strokes-ptv1", "macgyver-ptv3", "homem-que-veio-do-ceu", "pluto-tv-desenhos-classicos", "popeye-ptv1"]}, {"id": "643f0a535a0cd5000836336f", "name": "Novelas", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0a535a0cd5000836336f/pngImageUrl.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0a535a0cd5000836336f/svgImageUrl.svg"}], "channelIDs": ["5f512365abe1f50007d3ff56"], "channelSlugs": ["pluto-tv-novelas-ptv1"]}, {"id": "643f0ab3d3fdde0008ddcc6a", "name": "Reality", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0ab3d3fdde0008ddcc6a/pngImageUrl.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/643f0ab3d3fdde0008ddcc6a/svgImageUrl.svg"}], "channelIDs": ["6077045b6031bd00078de127", "6647c0b91050b60008390de4", "645111f1d8436e00081bb2bd", "5f1212fb81e85c00077ae9ef", "6851bdfc9ac48fde5e07f5ae", "6851bb3426beced4f2f67ee6", "625463563b8ddc0007134aeb", "61a528267e1b8b0007357920", "620fdc7d8a36fc000710e3ba", "626c2a3502d84a0007cec817", "5f6108d8cc331900075e98e4", "63988b61c8d285000798a22a"], "channelSlugs": ["masterchef-ptv1", "sony-one-shark-tank-brasil", "mtv-drag-ptv1", "mtv-pluto-tv1", "mtv-reality", "mtv-dating-gf-ptv1", "mtv-shore-ptv1", "mtv-com-o-ex", "mtv-jovens-maes", "mtv-catfish-ptv2", "mtv-are-you-the-one-ptv1", "mtv-just-tattoo-of-us"]}, {"id": "618ffcbb4527a9000786c89a", "name": "Curiosidades", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ffcbb4527a9000786c89a/pngImageUrl-1636826527786.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ffcbb4527a9000786c89a/svgImageUrl-1636826531106.svg"}], "channelIDs": ["5f1213ba0ecebc00070e170f", "604a8dedbca75b0007b1c753", "6474aa984cfc2c0008883a92", "61099df8cee03b00074b2ecf", "6014761dfb91870008ea6463", "6474aadc8e3c0a0008b98493", "5f32d4d9ec194100070c7449", "64ad7394798def00087b2bfe", "61843f5e491af10007f7eefa", "656e2a4b4261ca00083aa99e", "61bb72a7bf8c520007a8fd27", "5fac52f142044f00078e2a51", "6806d65e84f24b70109485fa", "620d1512c7986a0007220213", "656e2a81954b020008ed17a4", "656e2a10954b020008ed167c", "6298bd10d88ef000073f16b7", "5f1ef1a8cec6be00072a7ac9"], "channelSlugs": ["pluto-tv-natureza", "pluto-tv-paisagens-por-stingray", "pluto-tv-animais", "dog-whisperer-ptv1", "pluto-tv-turbo", "pluto-tv-curiosidade", "pluto-tv-vida-real-ptv1", "hardcore-pawn", "pluto-tv-minha-obsessao-favorita", "acumuladores-obsessivos", "pronto-socorro-historias-de-emergencia", "pluto-tv-misterios-ptv1", "pluto-tv-aliens", "assombracoes", "estado-paranormal", "cacadores-de-ovnis", "smithsonian-channel", "pluto-tv-documentarios"]}, {"id": "618ee29c4527a90007866bf6", "name": "Investigação", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee29c4527a90007866bf6/pngImageUrl-1636754411318.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee29c4527a90007866bf6/svgImageUrl-1636754414752.svg"}], "channelIDs": ["5f32cf37c9ff2b00082adbc8", "678fdf9e3de7c8cf948e8824", "620d12a82e8ac50007c269c3", "638df93ae2f2a3000737c168", "62b5c5a064163d0007b2efe6"], "channelSlugs": ["pluto-tv-investigacao-ptv1", "pluto-tv-policial", "os-arquivos-do-fbi", "detetives-medicos", "misterios-sem-solucao"]}, {"id": "618dd241ba73880008f48d27", "name": "Notícias", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618dd241ba73880008f48d27/pngImageUrl-1636753870505.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618dd241ba73880008f48d27/svgImageUrl-1636753875341.svg"}], "channelIDs": ["62310f66d5888f0007534342", "666c9c60a7efd40008f552f0", "619e6614c9d9650007a2b171", "6102e04e9ab1db0007a980a1", "6317ba014d4d040007227f72", "679a973a97782f0008ff4bb6"], "channelSlugs": ["cbs-news-ptv2", "bmandc-news", "euronews-portugues", "pluto-tv-record-news", "jovem-pan-news", "times-brasil-cnbc"]}, {"id": "67a0a51715d6eb000822fc32", "name": "TV Brasileira", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/67a0a51715d6eb000822fc32/pngImageUrl-1738581276094.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/67a0a51715d6eb000822fc32/svgImageUrl-1738581279829.svg"}], "channelIDs": ["645a99282858cb0008ea8378", "62e010e6cd663f0007e57dc8", "64b9370b409629000802d32b"], "channelSlugs": ["new-brasil", "tv-cultura", "pluto-tv-canal-uol"]}, {"id": "618ee4ef4527a90007866d01", "name": "Esportes", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee4ef4527a90007866d01/pngImageUrl-1636754801438.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee4ef4527a90007866d01/svgImageUrl-1636754806803.svg"}], "channelIDs": ["5f32d2db0af67400077f29c4", "66997e8d3a4ad20008e50be9", "63dac28760bc8f0008a7654b", "65a6818c7bdc8d0008457b21", "6660b636cb3ea10008429c6a", "64df7ab4dfa32400083c517b", "64f6180130ab3300083d896b", "6752f863cbfdc56330643e9b", "664f766741af640008e59cc9", "63eba66da8b2270008436b10"], "channelSlugs": ["pluto-tv-esportes", "fifa-ptv1", "real-madrid", "mavtv-brasil", "sft-combat", "dazn-combat-ptv2", "pfl-mma-ptv1", "pluto-tv-modus-super-series-darts-ptv2", "billiard-tv", "world-poker-tour-ptv1"]}, {"id": "65e8e1caf7f0af0008d9619e", "name": "South Park", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/65e8e1caf7f0af0008d9619e/pngImageUrl.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/MainCategories/65e8e1caf7f0af0008d9619e/svgImageUrl.svg"}], "channelIDs": ["65df71008b24c80008f04281", "65df704366eec8000898e32f", "65df713dec9fda0008b7a81d", "65df70b0f7f0af0008c3b316", "609ae66b359b270007869ff1"], "channelSlugs": ["south-park-colecao-cartman", "south-park-colecao-kenny", "south-park-colecao-kyle", "south-park-colecao-stan", "south-park-ptv1"]}, {"id": "618db0aa06d23b00073635fe", "name": "Comédia", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618db0aa06d23b00073635fe/pngImageUrl-1636754847633.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618db0aa06d23b00073635fe/svgImageUrl-1636754854510.svg"}], "channelIDs": ["6001f3018502100007f528ac", "5f357e91b18f0b00073583d2", "67802a22a5d8215f99dcee30", "5f5141c1605ddf000748eb1b", "5f515ebac01c0f00080e8439"], "channelSlugs": ["pluto-tv-comedia", "comedy-central-pluto-tv-ptv1", "pegadinhas-just-for-laughs", "failarmy-ptv1", "the-pet-collective-ptv1"]}, {"id": "618db179a11f4c00077073bb", "name": "Estilo de Vida", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618db179a11f4c00077073bb/pngImageUrl-1636755214691.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618db179a11f4c00077073bb/svgImageUrl-1636755218145.svg"}], "channelIDs": ["66a7d0e0cd0d310008448610", "5f32d432d612e50007e56133", "5f1ef23020a5ac0007e5e8ea", "633ee9ba83c08f00076b60a6", "5fd1419a3b4f4b000773ba85", "662156805a31f70013dba21c"], "channelSlugs": ["salon-line", "pluto-tv-viagens", "pluto-tv-cozinha", "pluto-tv-kfood", "tastemade-ptv2", "diatv"]}, {"id": "618ee3284a270700077bbe79", "name": "Anime & Geek", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee3284a270700077bbe79/pngImageUrl-1636754360228.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618ee3284a270700077bbe79/svgImageUrl-1636754364070.svg"}], "channelIDs": ["5f12136385bccc00070142ed", "604b79c558393100078faeef", "5f6df5a173d7340007c559f7", "64c92f965580090008084968", "656f389c3944b60008e5bdab", "65d9167818036500080e8780", "625464a945b6a200079257d1", "66c7982f6838ee00085f0d24", "63988c2750108d00072e2686", "63988a50be012600070f5db3", "646663b01593940008990a57", "633dc392e0282400071b0d39", "66b26681d2d50d00083abe8b", "624b1c8d4321e200073ee421", "5ff609de50ab210008025c1b", "605cc4de7ff3c50007be0f65", "664df5221dc48600087592b1", "64c815e8a1c6130008fef928"], "channelSlugs": ["pluto-tv-anime-1", "pluto-tv-anime-acao", "naruto-ptv1", "naruto-shippuden-ptv1", "boruto-naruto-next-generations-ptv1", "hunter-x-hunter", "death-note", "jojos-bizarre-adventure-ptv1", "super-onze", "yu-gi-oh-ptv2", "captain-tsu<PERSON>a-ptv1", "beyblade-ptv1", "inuyasha-ptv1", "one-piece-ptv2", "tokusato-ptv1", "pluto-tv-geek-tech", "manual-do-mundo", "pluto-tv-gaming-por-ubisoft-ptv1"]}, {"id": "618dd59d06d23b0007365f9b", "name": "Infantil", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618dd59d06d23b0007365f9b/pngImageUrl-1636755267970.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618dd59d06d23b0007365f9b/svgImageUrl-1636755271235.svg"}], "channelIDs": ["6824ce95f09106f4b18f4114", "63da6bcd60bc8f0008a5d364", "5f4fb4cf605ddf000748e16f", "5f12141b146d760007934ea7", "64e50055286f6b000838c067", "62d969fd8451a30007f0fd94", "5f5c216df68f920007888315", "6824ce10c5d53e1351ceb8d1", "6759eeb1bd523200083b4f29", "645951c0e94c38000802d2cb", "62545c0b002f4b0007688b61", "63221e41af69b500076f84e7", "63dd5c904e83e700088fb68a", "5f1214a637c6fd00079c652f", "66c8cae7fed35b0008580ec0", "63221bafdc6e110007b50270", "63eba189c111bc0008ff59c5", "5f997e44949bc70007a6941e", "66c8ca1280a0af00085b5b83"], "channelSlugs": ["nick-jr-club-ptv1", "baby-shark-tv-ptv2", "babyfirst-ptv1", "pluto-tv-junior-1", "teletubbies", "cocorico", "o-reino-infantil-ptv1", "nickelodeon-classico", "avatar-a-lend<PERSON>-de-aang", "nick-pluto-tv-ptv1", "bob-esponja-calca-quadrada", "os-padrinhos-magicos", "as-tartarugas-ninja", "pluto-tv-kids-1", "pluto-tv-kids-club", "oggy-e-as-baratas-tontas", "<PERSON><PERSON><PERSON><PERSON>", "turma-da-monica", "pluto-tv-cineminha"]}, {"id": "618c487e06d23b0007349292", "name": "Música", "images": [{"type": "pngImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618c487e06d23b0007349292/pngImageUrl-1636755286344.png"}, {"type": "svgImage", "style": "square", "ratio": 1, "defaultWidth": 90, "defaultHeight": 90, "url": "https://images.pluto.tv/maincategory/618c487e06d23b0007349292/svgImageUrl-1636755289982.svg"}], "channelIDs": ["6047fbdbbb776a0007e7f2ff", "66a01dcb8561260008b0a41d", "66a01e07d2d50d0008100d6a", "6047ff2e5e61cf000784e4da", "62b219eed88ef00007403c2c", "66a01d84fe11e500084aefc1", "604b91e0692f770007d9f33f", "604b99d633a72b00078e05ad"], "channelSlugs": ["mtv-biggest-pop-ptv2", "mtv-classic-ptv2", "mtv-rocks-ptv1", "mtv-spankin-new-ptv2", "mtv-miaw-pluto-tv-ptv1", "yo-mtv-ptv2", "pluto-tv-shows-por-stingray", "pluto-tv-karaoke-por-stingray"]}]}