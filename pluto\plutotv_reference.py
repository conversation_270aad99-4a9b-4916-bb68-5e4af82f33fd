# import requests

# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjRmZGE2YjE2LWFjYzItNGVmYS04ZjY5LTE2ZjQwMGJmNzdmMCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IG9e2UzSs3ftiXvm33DI3STcWUUDnaSrFbco7HzMT6c',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# params = {
#     'channelIds': '',
#     'offset': '0',
#     'limit': '1000',
#     'sort': 'number:asc',
# }

# response = requests.get('https://service-channels.clusters.pluto.tv/v2/guide/channels', params=params, headers=headers)

# print(len(response.json().get('data')))
# 396

# import requests
# import json
# from pathlib import Path

# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjRmZGE2YjE2LWFjYzItNGVmYS04ZjY5LTE2ZjQwMGJmNzdmMCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Zc2UAs0WKRhG46q_1Fh01emZGsQIpKs3aK7lJv0k-8M',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }
# path = Path(__file__).parent

# response = requests.get('https://service-channels.clusters.pluto.tv/v2/guide/categories', headers=headers)
# data = response.json().get('data')
# a = []
# for i in data:
#     for j in i.get('channelIDs'):
#         a.append(j)
# print(len(a))
# print(len(set(a)))

# import requests
# from pathlib import Path
# import json

# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjRmZGE2YjE2LWFjYzItNGVmYS04ZjY5LTE2ZjQwMGJmNzdmMCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IG9e2UzSs3ftiXvm33DI3STcWUUDnaSrFbco7HzMT6c',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# params = {
#     'offset': '1000',
#     'page': '1',
# }
# path = Path(__file__).parent


# response = requests.get(
#     'https://service-vod.clusters.pluto.tv/v4/vod/categories/6018833064d99100075a1e8a/items',
#     params=params,
#     headers=headers,
# )

# print(len(response.json().get('items')))
# print(response.json().get('items'))
# with open(path / 'items.json', 'w', encoding='utf-8') as f:
#     f.write(json.dumps(response.json().get('items')))


# import requests
# from pathlib import Path
# import json
# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjRmZGE2YjE2LWFjYzItNGVmYS04ZjY5LTE2ZjQwMGJmNzdmMCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IG9e2UzSs3ftiXvm33DI3STcWUUDnaSrFbco7HzMT6c',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# params = {
#     'start': '2025-07-09T03:30:00.000Z',
#     'channelIds': '561d7d484dc7c8770484914a',
#     'duration': '1440',
#     # 'limit': '1000',
# }
# path = Path(__file__).parent

# response = requests.get('https://service-channels.clusters.pluto.tv/v2/guide/timelines', params=params, headers=headers)
# print(response.json())
# print(response.json().get('data')[0].get('timelines'))
# with open(path / 'timelines.json', 'w', encoding='utf-8') as f:
#     f.write(json.dumps(response.json()))


import requests

headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
    'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjhlMTE5ZmMyLTY5MjQtNGVkMy1iMjQxLTlmYTBiMTAzMzM1OCIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7-1kf3G5PnyHrPjLCgRtAAwM-anLknGEevMc5T2a788',
    'cache-control': 'no-cache',
    'origin': 'https://pluto.tv',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://pluto.tv/',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-id-token': 'eyJhbGciOiJIUzI1NiIsImtpZCI6Ijc4NGQwZDAxLWRiNzktNDE2ZC1hZTBkLTRhN2E5N2E0OTZmNyIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODZlMjQyNzg2YjZhMjcyNjY4ODAzMmYiLCJlbWFpbEhhc2giOiI5ZTdmN2IyYWE0ODBhZjU5NTA3NTZkNGRmNGE3MjA5OWFmMTk0MWVhMWI3NDJkY2VkNGE2ZWRkM2RlY2U4NzFhIiwiYWdlIjoyLCJnZW5kZXIiOjAsImJyYW5kIjoicGx1dG90diIsIm9yaWdpbmF0aW5nU291cmNlIjoicGx1dG8iLCJpc3MiOiJzZXJ2aWNlLXVzZXJzLmNsdXN0ZXJzLnBsdXRvLnR2IiwiYXVkIjoiKi5wbHV0by50diIsImV4cCI6MTc1MjIxMzc2NywiaWF0IjoxNzUyMTI3MzY3LCJqdGkiOiI1NjQyYjg0Mi1kNzMzLTQ2OWQtODBmZS0yM2FmODEyMjg1NjgifQ.H_wX2-d3hZVgV1sesEiad_-gcaImhFDzn69we13YHbU',
}

params = {
    'constraints': '',
}

response = requests.get('https://boot.pluto.tv/v4/refresh', params=params, headers=headers)
print(response.json())

# import requests
# from pathlib import Path
# import json
# headers = {
#     'accept': '*/*',
#     'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,en-GB-oxendict;q=0.5,zh-TW;q=0.4,ja;q=0.3',
#     'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IjhlMTE5ZmMyLTY5MjQtNGVkMy1iMjQxLTlmYTBiMTAzMzM1OCIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.C2e3pPzcsDc5_AXDq1KHo8k1Iheqn_EwdW_dBwz-fHc',
#     'cache-control': 'no-cache',
#     'origin': 'https://pluto.tv',
#     'pragma': 'no-cache',
#     'priority': 'u=1, i',
#     'referer': 'https://pluto.tv/',
#     'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
#     'sec-ch-ua-mobile': '?0',
#     'sec-ch-ua-platform': '"Windows"',
#     'sec-fetch-dest': 'empty',
#     'sec-fetch-mode': 'cors',
#     'sec-fetch-site': 'same-site',
#     'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
# }

# response = requests.get('https://service-channels.clusters.pluto.tv/v2/guide/categories', headers=headers)
# with open(Path(__file__).parent / 'categories.json', 'w', encoding='utf-8') as f:
#     f.write(json.dumps(response.json()))
# data = response.json().get('data')
# a = []
# for i in data:
#     for j in i.get('channelIDs'):
#         a.append(j)
# print(len(a))
# print(len(set(a)))